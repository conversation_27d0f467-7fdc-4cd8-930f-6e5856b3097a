a:14:{s:6:"config";s:1743:"a:5:{s:10:"phpVersion";s:5:"8.1.9";s:10:"yiiVersion";s:6:"2.0.52";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.52";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.1.9";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:6:{s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:61:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:56:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:56:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:54:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:64:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:61:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:9062:"a:1:{s:8:"messages";a:16:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752903190.021244;i:4;a:0:{}i:5;i:2602360;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752903190.02284;i:4;a:0:{}i:5;i:2780720;}i:2;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752903190.022854;i:4;a:0:{}i:5;i:2781520;}i:3;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:1752903190.033005;i:4;a:0:{}i:5;i:3560288;}i:4;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752903190.042979;i:4;a:0:{}i:5;i:4171856;}i:5;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752903190.057028;i:4;a:0:{}i:5;i:4614528;}i:6;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752903190.057883;i:4;a:0:{}i:5;i:4639360;}i:57;a:6:{i:0;s:42:"Route requested: 'employer/vacancy/update'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1752903190.062364;i:4;a:0:{}i:5;i:4910928;}i:58;a:6:{i:0;s:24:"Loading module: employer";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752903190.062382;i:4;a:0:{}i:5;i:4912568;}i:59;a:6:{i:0;s:37:"Route to run: employer/vacancy/update";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1752903190.07069;i:4;a:0:{}i:5;i:5206648;}i:60;a:6:{i:0;s:39:"Rate limit skipped: user not logged in.";i:1;i:4;i:2;s:37:"yii\filters\RateLimiter::beforeAction";i:3;d:1752903190.075677;i:4;a:0:{}i:5;i:5419304;}i:61;a:6:{i:0;s:82:"Running action: app\modules\employer\controllers\VacancyController::actionUpdate()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1752903190.075812;i:4;a:0:{}i:5;i:5420576;}i:62;a:6:{i:0;s:58:"Opening DB connection: pgsql:host=localhost;dbname=ish_top";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1752903190.112865;i:4;a:3:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\ish_top\modules\employer\models\Employer.php";s:4:"line";i:177;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:217;s:8:"function";s:15:"findByAuthToken";s:5:"class";s:36:"app\modules\employer\models\Employer";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:229;s:8:"function";s:18:"getCurrentEmployer";s:5:"class";s:50:"app\modules\employer\controllers\BaseApiController";s:4:"type";s:2:"->";}}i:5;i:7173520;}i:65;a:6:{i:0;s:189:"SELECT * FROM "employers" WHERE ("auth_token"='Dg8O4KM99PBf-v7t-1RakqjVzc3mQEQFSQ9GuXZNzvbenMeZg0m10EiaCzW4mh4s') AND ("token_expires_at" > '2025-07-19 08:33:10') AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752903190.180363;i:4;a:3:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\ish_top\modules\employer\models\Employer.php";s:4:"line";i:177;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:217;s:8:"function";s:15:"findByAuthToken";s:5:"class";s:36:"app\modules\employer\models\Employer";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:229;s:8:"function";s:18:"getCurrentEmployer";s:5:"class";s:50:"app\modules\employer\controllers\BaseApiController";s:4:"type";s:2:"->";}}i:5;i:7183552;}i:68;a:6:{i:0;s:2815:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'employers'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752903190.189031;i:4;a:3:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\ish_top\modules\employer\models\Employer.php";s:4:"line";i:177;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:217;s:8:"function";s:15:"findByAuthToken";s:5:"class";s:36:"app\modules\employer\models\Employer";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:229;s:8:"function";s:18:"getCurrentEmployer";s:5:"class";s:50:"app\modules\employer\controllers\BaseApiController";s:4:"type";s:2:"->";}}i:5;i:7227136;}i:71;a:6:{i:0;s:877:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='employers'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752903190.220896;i:4;a:3:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\ish_top\modules\employer\models\Employer.php";s:4:"line";i:177;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:217;s:8:"function";s:15:"findByAuthToken";s:5:"class";s:36:"app\modules\employer\models\Employer";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:229;s:8:"function";s:18:"getCurrentEmployer";s:5:"class";s:50:"app\modules\employer\controllers\BaseApiController";s:4:"type";s:2:"->";}}i:5;i:7276152;}}}";s:9:"profiling";s:14339:"a:3:{s:6:"memory";i:7705624;s:4:"time";d:0.2190999984741211;s:8:"messages";a:8:{i:63;a:6:{i:0;s:58:"Opening DB connection: pgsql:host=localhost;dbname=ish_top";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1752903190.112909;i:4;a:3:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\ish_top\modules\employer\models\Employer.php";s:4:"line";i:177;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:217;s:8:"function";s:15:"findByAuthToken";s:5:"class";s:36:"app\modules\employer\models\Employer";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:229;s:8:"function";s:18:"getCurrentEmployer";s:5:"class";s:50:"app\modules\employer\controllers\BaseApiController";s:4:"type";s:2:"->";}}i:5;i:7175400;}i:64;a:6:{i:0;s:58:"Opening DB connection: pgsql:host=localhost;dbname=ish_top";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1752903190.180281;i:4;a:3:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\ish_top\modules\employer\models\Employer.php";s:4:"line";i:177;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:217;s:8:"function";s:15:"findByAuthToken";s:5:"class";s:36:"app\modules\employer\models\Employer";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:229;s:8:"function";s:18:"getCurrentEmployer";s:5:"class";s:50:"app\modules\employer\controllers\BaseApiController";s:4:"type";s:2:"->";}}i:5;i:7178072;}i:66;a:6:{i:0;s:189:"SELECT * FROM "employers" WHERE ("auth_token"='Dg8O4KM99PBf-v7t-1RakqjVzc3mQEQFSQ9GuXZNzvbenMeZg0m10EiaCzW4mh4s') AND ("token_expires_at" > '2025-07-19 08:33:10') AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752903190.180415;i:4;a:3:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\ish_top\modules\employer\models\Employer.php";s:4:"line";i:177;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:217;s:8:"function";s:15:"findByAuthToken";s:5:"class";s:36:"app\modules\employer\models\Employer";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:229;s:8:"function";s:18:"getCurrentEmployer";s:5:"class";s:50:"app\modules\employer\controllers\BaseApiController";s:4:"type";s:2:"->";}}i:5;i:7186464;}i:67;a:6:{i:0;s:189:"SELECT * FROM "employers" WHERE ("auth_token"='Dg8O4KM99PBf-v7t-1RakqjVzc3mQEQFSQ9GuXZNzvbenMeZg0m10EiaCzW4mh4s') AND ("token_expires_at" > '2025-07-19 08:33:10') AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752903190.187646;i:4;a:3:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\ish_top\modules\employer\models\Employer.php";s:4:"line";i:177;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:217;s:8:"function";s:15:"findByAuthToken";s:5:"class";s:36:"app\modules\employer\models\Employer";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:229;s:8:"function";s:18:"getCurrentEmployer";s:5:"class";s:50:"app\modules\employer\controllers\BaseApiController";s:4:"type";s:2:"->";}}i:5;i:7190384;}i:69;a:6:{i:0;s:2815:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'employers'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752903190.189074;i:4;a:3:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\ish_top\modules\employer\models\Employer.php";s:4:"line";i:177;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:217;s:8:"function";s:15:"findByAuthToken";s:5:"class";s:36:"app\modules\employer\models\Employer";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:229;s:8:"function";s:18:"getCurrentEmployer";s:5:"class";s:50:"app\modules\employer\controllers\BaseApiController";s:4:"type";s:2:"->";}}i:5;i:7229376;}i:70;a:6:{i:0;s:2815:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'employers'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752903190.219562;i:4;a:3:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\ish_top\modules\employer\models\Employer.php";s:4:"line";i:177;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:217;s:8:"function";s:15:"findByAuthToken";s:5:"class";s:36:"app\modules\employer\models\Employer";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:229;s:8:"function";s:18:"getCurrentEmployer";s:5:"class";s:50:"app\modules\employer\controllers\BaseApiController";s:4:"type";s:2:"->";}}i:5;i:7251928;}i:72;a:6:{i:0;s:877:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='employers'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752903190.22093;i:4;a:3:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\ish_top\modules\employer\models\Employer.php";s:4:"line";i:177;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:217;s:8:"function";s:15:"findByAuthToken";s:5:"class";s:36:"app\modules\employer\models\Employer";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:229;s:8:"function";s:18:"getCurrentEmployer";s:5:"class";s:50:"app\modules\employer\controllers\BaseApiController";s:4:"type";s:2:"->";}}i:5;i:7278392;}i:73;a:6:{i:0;s:877:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='employers'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752903190.224173;i:4;a:3:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\ish_top\modules\employer\models\Employer.php";s:4:"line";i:177;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:217;s:8:"function";s:15:"findByAuthToken";s:5:"class";s:36:"app\modules\employer\models\Employer";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:229;s:8:"function";s:18:"getCurrentEmployer";s:5:"class";s:50:"app\modules\employer\controllers\BaseApiController";s:4:"type";s:2:"->";}}i:5;i:7280704;}}}";s:2:"db";s:12572:"a:1:{s:8:"messages";a:6:{i:66;a:6:{i:0;s:189:"SELECT * FROM "employers" WHERE ("auth_token"='Dg8O4KM99PBf-v7t-1RakqjVzc3mQEQFSQ9GuXZNzvbenMeZg0m10EiaCzW4mh4s') AND ("token_expires_at" > '2025-07-19 08:33:10') AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752903190.180415;i:4;a:3:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\ish_top\modules\employer\models\Employer.php";s:4:"line";i:177;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:217;s:8:"function";s:15:"findByAuthToken";s:5:"class";s:36:"app\modules\employer\models\Employer";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:229;s:8:"function";s:18:"getCurrentEmployer";s:5:"class";s:50:"app\modules\employer\controllers\BaseApiController";s:4:"type";s:2:"->";}}i:5;i:7186464;}i:67;a:6:{i:0;s:189:"SELECT * FROM "employers" WHERE ("auth_token"='Dg8O4KM99PBf-v7t-1RakqjVzc3mQEQFSQ9GuXZNzvbenMeZg0m10EiaCzW4mh4s') AND ("token_expires_at" > '2025-07-19 08:33:10') AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752903190.187646;i:4;a:3:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\ish_top\modules\employer\models\Employer.php";s:4:"line";i:177;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:217;s:8:"function";s:15:"findByAuthToken";s:5:"class";s:36:"app\modules\employer\models\Employer";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:229;s:8:"function";s:18:"getCurrentEmployer";s:5:"class";s:50:"app\modules\employer\controllers\BaseApiController";s:4:"type";s:2:"->";}}i:5;i:7190384;}i:69;a:6:{i:0;s:2815:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'employers'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752903190.189074;i:4;a:3:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\ish_top\modules\employer\models\Employer.php";s:4:"line";i:177;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:217;s:8:"function";s:15:"findByAuthToken";s:5:"class";s:36:"app\modules\employer\models\Employer";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:229;s:8:"function";s:18:"getCurrentEmployer";s:5:"class";s:50:"app\modules\employer\controllers\BaseApiController";s:4:"type";s:2:"->";}}i:5;i:7229376;}i:70;a:6:{i:0;s:2815:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'employers'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752903190.219562;i:4;a:3:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\ish_top\modules\employer\models\Employer.php";s:4:"line";i:177;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:217;s:8:"function";s:15:"findByAuthToken";s:5:"class";s:36:"app\modules\employer\models\Employer";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:229;s:8:"function";s:18:"getCurrentEmployer";s:5:"class";s:50:"app\modules\employer\controllers\BaseApiController";s:4:"type";s:2:"->";}}i:5;i:7251928;}i:72;a:6:{i:0;s:877:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='employers'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752903190.22093;i:4;a:3:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\ish_top\modules\employer\models\Employer.php";s:4:"line";i:177;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:217;s:8:"function";s:15:"findByAuthToken";s:5:"class";s:36:"app\modules\employer\models\Employer";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:229;s:8:"function";s:18:"getCurrentEmployer";s:5:"class";s:50:"app\modules\employer\controllers\BaseApiController";s:4:"type";s:2:"->";}}i:5;i:7278392;}i:73;a:6:{i:0;s:877:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='employers'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752903190.224173;i:4;a:3:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\ish_top\modules\employer\models\Employer.php";s:4:"line";i:177;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:217;s:8:"function";s:15:"findByAuthToken";s:5:"class";s:36:"app\modules\employer\models\Employer";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\ish_top\modules\employer\controllers\BaseApiController.php";s:4:"line";i:229;s:8:"function";s:18:"getCurrentEmployer";s:5:"class";s:50:"app\modules\employer\controllers\BaseApiController";s:4:"type";s:2:"->";}}i:5;i:7280704;}}}";s:5:"event";s:2737:"a:15:{i:0;a:5:{s:4:"time";d:1752903190.061093;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:1752903190.071023;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:1752903190.07105;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"app\modules\employer\Module";}i:3;a:5:{s:4:"time";d:1752903190.075784;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:50:"app\modules\employer\controllers\VacancyController";}i:4;a:5:{s:4:"time";d:1752903190.092159;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:5;a:5:{s:4:"time";d:1752903190.180262;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:6;a:5:{s:4:"time";d:1752903190.188673;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\modules\employer\models\Employer";}i:7;a:5:{s:4:"time";d:1752903190.224899;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\modules\employer\models\Employer";}i:8;a:5:{s:4:"time";d:1752903190.226522;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:50:"app\modules\employer\controllers\VacancyController";}i:9;a:5:{s:4:"time";d:1752903190.22695;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"app\modules\employer\Module";}i:10;a:5:{s:4:"time";d:1752903190.226959;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:11;a:5:{s:4:"time";d:1752903190.226968;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:12;a:5:{s:4:"time";d:1752903190.226974;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:13;a:5:{s:4:"time";d:1752903190.230151;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:14;a:5:{s:4:"time";d:1752903190.230202;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:1752903190.013068;s:3:"end";d:1752903190.232265;s:6:"memory";i:7705624;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:10037:"a:3:{s:8:"messages";a:50:{i:7;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.061867;i:4;a:0:{}i:5;i:4870024;}i:8;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.061896;i:4;a:0:{}i:5;i:4870776;}i:9;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.061909;i:4;a:0:{}i:5;i:4871848;}i:10;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.061919;i:4;a:0:{}i:5;i:4872600;}i:11;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.061929;i:4;a:0:{}i:5;i:4873352;}i:12;a:6:{i:0;a:3:{s:4:"rule";s:16:"telegram/webhook";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.061942;i:4;a:0:{}i:5;i:4874104;}i:13;a:6:{i:0;a:3:{s:4:"rule";s:34:"telegram/registration/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.061952;i:4;a:0:{}i:5;i:4874856;}i:14;a:6:{i:0;a:3:{s:4:"rule";s:32:"telegram/profession/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.061961;i:4;a:0:{}i:5;i:4875608;}i:15;a:6:{i:0;a:3:{s:4:"rule";s:26:"POST worker/auth/send-code";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.061983;i:4;a:0:{}i:5;i:4876416;}i:16;a:6:{i:0;a:3:{s:4:"rule";s:22:"POST worker/auth/login";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.061994;i:4;a:0:{}i:5;i:4877216;}i:17;a:6:{i:0;a:3:{s:4:"rule";s:23:"POST worker/auth/logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062003;i:4;a:0:{}i:5;i:4878656;}i:18;a:6:{i:0;a:3:{s:4:"rule";s:22:"GET worker/auth/verify";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062012;i:4;a:0:{}i:5;i:4879456;}i:19;a:6:{i:0;a:3:{s:4:"rule";s:24:"POST worker/auth/refresh";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062021;i:4;a:0:{}i:5;i:4880264;}i:20;a:6:{i:0;a:3:{s:4:"rule";s:23:"GET worker/vacancy/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.06203;i:4;a:0:{}i:5;i:4881064;}i:21;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET worker/vacancy/search";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062037;i:4;a:0:{}i:5;i:4881872;}i:22;a:6:{i:0;a:3:{s:4:"rule";s:34:"GET worker/vacancy/detail/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062045;i:4;a:0:{}i:5;i:4882688;}i:23;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET worker/profile/index";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062052;i:4;a:0:{}i:5;i:4883496;}i:24;a:6:{i:0;a:3:{s:4:"rule";s:25:"PUT worker/profile/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062063;i:4;a:0:{}i:5;i:4884304;}i:25;a:6:{i:0;a:3:{s:4:"rule";s:32:"POST worker/profile/upload-audio";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.06207;i:4;a:0:{}i:5;i:4885120;}i:26;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/auth/send-code";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062078;i:4;a:0:{}i:5;i:4885928;}i:27;a:6:{i:0;a:3:{s:4:"rule";s:30:"POST employer/auth/verify-code";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062085;i:4;a:0:{}i:5;i:4886736;}i:28;a:6:{i:0;a:3:{s:4:"rule";s:40:"POST employer/auth/complete-registration";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062093;i:4;a:0:{}i:5;i:4887568;}i:29;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET employer/auth/status";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062101;i:4;a:0:{}i:5;i:4888376;}i:30;a:6:{i:0;a:3:{s:4:"rule";s:25:"POST employer/auth/logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062109;i:4;a:0:{}i:5;i:4889184;}i:31;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET employer/worker/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062118;i:4;a:0:{}i:5;i:4889992;}i:32;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET employer/worker/search";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062127;i:4;a:0:{}i:5;i:4890800;}i:33;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET employer/worker/detail";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062137;i:4;a:0:{}i:5;i:4892888;}i:34;a:6:{i:0;a:3:{s:4:"rule";s:31:"GET employer/worker/professions";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062146;i:4;a:0:{}i:5;i:4893696;}i:35;a:6:{i:0;a:3:{s:4:"rule";s:33:"GET employer/worker/by-profession";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062155;i:4;a:0:{}i:5;i:4894512;}i:36;a:6:{i:0;a:3:{s:4:"rule";s:30:"GET employer/worker/statistics";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062163;i:4;a:0:{}i:5;i:4895320;}i:37;a:6:{i:0;a:3:{s:4:"rule";s:32:"GET employer/worker/check-access";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062172;i:4;a:0:{}i:5;i:4896136;}i:38;a:6:{i:0;a:3:{s:4:"rule";s:26:"POST employer/favorite/add";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062181;i:4;a:0:{}i:5;i:4896944;}i:39;a:6:{i:0;a:3:{s:4:"rule";s:29:"POST employer/favorite/remove";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062189;i:4;a:0:{}i:5;i:4897752;}i:40;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET employer/favorite/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062198;i:4;a:0:{}i:5;i:4898560;}i:41;a:6:{i:0;a:3:{s:4:"rule";s:29:"POST employer/favorite/toggle";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062206;i:4;a:0:{}i:5;i:4899368;}i:42;a:6:{i:0;a:3:{s:4:"rule";s:31:"POST employer/favorite/bulk-add";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062215;i:4;a:0:{}i:5;i:4900176;}i:43;a:6:{i:0;a:3:{s:4:"rule";s:34:"POST employer/favorite/bulk-remove";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062223;i:4;a:0:{}i:5;i:4900992;}i:44;a:6:{i:0;a:3:{s:4:"rule";s:32:"GET employer/favorite/statistics";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062232;i:4;a:0:{}i:5;i:4901808;}i:45;a:6:{i:0;a:3:{s:4:"rule";s:35:"GET employer/favorite/by-profession";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062242;i:4;a:0:{}i:5;i:4902624;}i:46;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET employer/profile/view";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.06225;i:4;a:0:{}i:5;i:4903432;}i:47;a:6:{i:0;a:3:{s:4:"rule";s:27:"PUT employer/profile/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062262;i:4;a:0:{}i:5;i:4904240;}i:48;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/profile/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062271;i:4;a:0:{}i:5;i:4905048;}i:49;a:6:{i:0;a:3:{s:4:"rule";s:37:"POST employer/profile/change-language";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062279;i:4;a:0:{}i:5;i:4905864;}i:50;a:6:{i:0;a:3:{s:4:"rule";s:30:"GET employer/profile/languages";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062288;i:4;a:0:{}i:5;i:4906672;}i:51;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/profile/delete";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062297;i:4;a:0:{}i:5;i:4907480;}i:52;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET employer/vacancy/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062305;i:4;a:0:{}i:5;i:4908288;}i:53;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET employer/vacancy/view";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062314;i:4;a:0:{}i:5;i:4909096;}i:54;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/vacancy/create";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.062322;i:4;a:0:{}i:5;i:4909904;}i:55;a:6:{i:0;s:53:"Request parsed with URL rule: employer/vacancy/update";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:1752903190.06234;i:4;a:0:{}i:5;i:4911192;}i:56;a:6:{i:0;a:3:{s:4:"rule";s:27:"PUT employer/vacancy/update";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752903190.06235;i:4;a:0:{}i:5;i:4911544;}}s:5:"route";s:23:"employer/vacancy/update";s:6:"action";s:66:"app\modules\employer\controllers\VacancyController::actionUpdate()";}";s:7:"request";s:3747:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:422;s:14:"requestHeaders";a:11:{s:13:"authorization";s:71:"Bearer Dg8O4KM99PBf-v7t-1RakqjVzc3mQEQFSQ9GuXZNzvbenMeZg0m10EiaCzW4mh4s";s:12:"content-type";s:16:"application/json";s:10:"user-agent";s:21:"PostmanRuntime/7.44.1";s:6:"accept";s:3:"*/*";s:13:"cache-control";s:8:"no-cache";s:13:"postman-token";s:36:"7d47880e-076d-41af-a5e2-6f1e859aaa9b";s:4:"host";s:7:"vacanct";s:15:"accept-encoding";s:17:"gzip, deflate, br";s:10:"connection";s:10:"keep-alive";s:14:"content-length";s:2:"80";s:6:"cookie";s:42:"PHPSESSID=c6fftfvaem9sb0kvg841i04ktsj7ppcu";}s:15:"responseHeaders";a:8:{s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:32:"Access-Control-Allow-Credentials";s:5:"false";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"687b2e160ad57";s:16:"X-Debug-Duration";s:3:"218";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=687b2e160ad57";}s:5:"route";s:23:"employer/vacancy/update";s:6:"action";s:66:"app\modules\employer\controllers\VacancyController::actionUpdate()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:3:"PUT";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:3:{s:12:"Content Type";s:16:"application/json";s:3:"Raw";s:80:"{
    "id": 1,
    "title": "Updated Senior Developer",
    "status": "paused"
}";s:7:"Decoded";a:1:{s:80:"{
____"id":_1,
____"title":_"Updated_Senior_Developer",
____"status":_"paused"
}";s:0:"";}}s:6:"SERVER";a:39:{s:15:"REDIRECT_STATUS";s:3:"200";s:12:"CONTENT_TYPE";s:16:"application/json";s:15:"HTTP_USER_AGENT";s:21:"PostmanRuntime/7.44.1";s:11:"HTTP_ACCEPT";s:3:"*/*";s:18:"HTTP_CACHE_CONTROL";s:8:"no-cache";s:18:"HTTP_POSTMAN_TOKEN";s:36:"7d47880e-076d-41af-a5e2-6f1e859aaa9b";s:9:"HTTP_HOST";s:7:"vacanct";s:20:"HTTP_ACCEPT_ENCODING";s:17:"gzip, deflate, br";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:14:"CONTENT_LENGTH";s:2:"80";s:11:"HTTP_COOKIE";s:42:"PHPSESSID=c6fftfvaem9sb0kvg841i04ktsj7ppcu";s:4:"PATH";s:343:"d:\ospanel\modules\php\PHP_8.1\ext;d:\ospanel\modules\php\PHP_8.1\pear;d:\ospanel\modules\php\PHP_8.1\pear\bin;d:\ospanel\modules\php\PHP_8.1;d:\ospanel\modules\wget\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32\Wbem;C:\WINDOWS\SysWOW64";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:0:"";s:15:"SERVER_SOFTWARE";s:6:"Apache";s:11:"SERVER_NAME";s:7:"vacanct";s:11:"SERVER_ADDR";s:9:"127.0.0.1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:13:"DOCUMENT_ROOT";s:30:"D:/OSPanel/domains/ish_top/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:30:"D:/OSPanel/domains/ish_top/web";s:12:"SERVER_ADMIN";s:18:"[no address given]";s:15:"SCRIPT_FILENAME";s:40:"D:/OSPanel/domains/ish_top/web/index.php";s:11:"REMOTE_PORT";s:5:"56849";s:12:"REDIRECT_URL";s:24:"/employer/vacancy/update";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:3:"PUT";s:12:"QUERY_STRING";s:0:"";s:11:"REQUEST_URI";s:24:"/employer/vacancy/update";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1752903190.002496;s:12:"REQUEST_TIME";i:1752903190;}s:3:"GET";a:0:{}s:4:"POST";a:0:{}s:6:"COOKIE";a:1:{s:9:"PHPSESSID";s:32:"c6fftfvaem9sb0kvg841i04ktsj7ppcu";}s:5:"FILES";a:0:{}s:7:"SESSION";a:1:{s:7:"__flash";a:0:{}}}";s:4:"user";s:2:"N;";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"687b2e160ad57";s:3:"url";s:38:"http://vacanct/employer/vacancy/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752903190.002496;s:10:"statusCode";i:422;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7705624;s:14:"processingTime";d:0.2190999984741211;}s:10:"exceptions";a:0:{}}