# API Справочник - Модуль Worker

## Базовая информация
- **Базовый URL:** `https://yourdomain.com/worker/`
- **Формат:** JSON
- **Авторизация:** Bearer Token в заголовке `Authorization: Bearer TOKEN`
- **Язык:** Заголовок `Accept-Language: ru|uz|en`

## Структура ответов
```json
{
  "success": true|false,
  "data": {},
  "message": "Сообщение"
}
```

---

## Аутентификация

### Отправка SMS кода
**POST** `/worker/auth/send-code`
- **Авторизация:** Не требуется

**Параметры:**
- `phone` (string) - Номер телефона

**Запрос:**
```json
{
  "phone": "+998901234567"
}
```

**Ответ:**
```json
{
  "success": true,
  "data": {
    "message": "Код валидации отправлен на ваш телефон",
    "phone": "+998901234567",
    "expires_in": 300,
    "next_request_in": 120,
    "is_new_registration": true,
    "profile_status": 0
  }
}
```

### Авторизация
**POST** `/worker/auth/login`
- **Авторизация:** Не требуется

**Параметры:**
- `phone` (string) - Номер телефона
- `code` (string) - SMS код

**Запрос:**
```json
{
  "phone": "+998901234567",
  "code": "1234"
}
```

**Ответ:**
```json
{
  "success": true,
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "Bearer",
    "expires_at": "2024-01-15 12:00:00",
    "worker": {
      "id": 1,
      "name": "Иван Иванов",
      "phone": "+998901234567",
      "age": 25,
      "profile_status": "incomplete"
    }
  }
}
```

### Выход
**POST** `/worker/auth/logout`
- **Авторизация:** Требуется

**Параметры:** Нет

**Запрос:** Пустое тело

**Ответ:**
```json
{
  "success": true,
  "data": null,
  "message": "Успешный выход из системы"
}
```

### Проверка токена
**GET** `/worker/auth/verify`
- **Авторизация:** Требуется

**Параметры:** Нет

**Ответ:**
```json
{
  "success": true,
  "data": {
    "valid": true,
    "expires_at": "2024-01-15 12:00:00",
    "worker": {
      "id": 1,
      "name": "Иван Иванов",
      "phone": "+998901234567"
    }
  }
}
```

### Обновление токена
**POST** `/worker/auth/refresh`
- **Авторизация:** Требуется

**Параметры:** Нет

**Запрос:** Пустое тело

**Ответ:**
```json
{
  "success": true,
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "Bearer",
    "expires_at": "2024-01-22 12:00:00"
  }
}
```

---

## Профиль

### Просмотр профиля
**GET** `/worker/profile/index`
- **Авторизация:** Требуется

**Параметры:** Нет

**Ответ:**
```json
{
  "success": true,
  "data": {
    "worker": {
      "id": 1,
      "name": "Иван Иванов",
      "phone": "+998901234567",
      "age": 25,
      "about": "Описание",
      "profile_status": "complete",
      "professions": [
        {"id": 1, "name": "Программист"},
        {"id": 2, "name": "Дизайнер"}
      ]
    }
  }
}
```

### Обновление профиля
**PUT** `/worker/profile/update`
- **Авторизация:** Требуется

**Параметры:**
- `name` (string, необязательный) - Имя
- `age` (integer, необязательный) - Возраст
- `about` (string, необязательный) - О себе
- `profession_ids` (array, необязательный) - ID профессий

**Запрос:**
```json
{
  "name": "Иван Петров",
  "age": 26,
  "about": "Опытный разработчик",
  "profession_ids": [1, 2, 3]
}
```

**Ответ:**
```json
{
  "success": true,
  "data": {
    "worker": {
      "id": 1,
      "name": "Иван Петров",
      "age": 26,
      "about": "Опытный разработчик",
      "profile_status": "complete"
    }
  }
}
```

### Загрузка аудио
**POST** `/worker/profile/upload-audio`
- **Авторизация:** Требуется
- **Content-Type:** `multipart/form-data`

**Параметры:**
- `audio` (file) - Аудио файл

**Ответ:**
```json
{
  "success": true,
  "data": {
    "audio_url": "https://yourdomain.com/uploads/audio/file.mp3",
    "message": "Аудио файл загружен успешно"
  }
}
```

---

## Вакансии

### Список вакансий
**GET** `/worker/vacancy/list`
- **Авторизация:** Требуется + завершенный профиль

**Параметры (query string):**
- `page` (integer, необязательный) - Номер страницы (по умолчанию 1)
- `per_page` (integer, необязательный) - Количество на странице (по умолчанию 10)

**Ответ:**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "title": "Программист PHP",
        "company": "IT Company",
        "salary": "1000000",
        "location": "Ташкент"
      }
    ],
    "pagination": {
      "total": 50,
      "page": 1,
      "per_page": 10,
      "total_pages": 5
    }
  }
}
```

### Поиск вакансий
**GET** `/worker/vacancy/search`
- **Авторизация:** Требуется + завершенный профиль

**Параметры (query string):**
- `q` (string) - Поисковый запрос
- `page` (integer, необязательный) - Номер страницы
- `per_page` (integer, необязательный) - Количество на странице

**Ответ:**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "title": "Программист PHP",
        "company": "IT Company",
        "salary": "1000000",
        "location": "Ташкент"
      }
    ],
    "pagination": {
      "total": 15,
      "page": 1,
      "per_page": 10,
      "total_pages": 2
    }
  }
}
```

### Детали вакансии
**GET** `/worker/vacancy/detail/{id}`
- **Авторизация:** Требуется + завершенный профиль

**Параметры:** 
- `{id}` в URL - ID вакансии

**Ответ:**
```json
{
  "success": true,
  "data": {
    "vacancy": {
      "id": 1,
      "title": "Программист PHP",
      "description": "Полное описание вакансии",
      "company": "IT Company",
      "salary": "1000000",
      "location": "Ташкент",
      "requirements": "Требования к кандидату",
      "created_at": "2024-01-01 10:00:00"
    }
  }
}
```

---

## Коды ошибок

| Код | Описание |
|-----|----------|
| 400 | Неверный запрос |
| 401 | Не авторизован |
| 403 | Требуется завершение профиля |
| 404 | Не найдено |
| 422 | Ошибка валидации |
| 429 | Слишком много запросов |
| 500 | Внутренняя ошибка |

## Примеры ошибок

**Ошибка валидации (422):**
```json
{
  "success": false,
  "data": {
    "phone": ["Номер телефона обязателен"],
    "code": ["Код валидации обязателен"]
  },
  "message": "Ошибка валидации"
}
```

**Rate Limiting (429):**
```json
{
  "success": false,
  "data": {
    "remaining_seconds": 85
  },
  "message": "Подождите 85 секунд перед запросом нового кода"
}
```

**Требуется завершение профиля (403):**
```json
{
  "success": false,
  "data": {
    "profile_completion_required": true,
    "completion_steps": {
      "name": true,
      "age": true,
      "about": false,
      "professions": true
    }
  },
  "message": "Требуется завершение профиля"
}
```
