<?php

namespace app\modules\telegram\models;

use yii\base\Component;
use yii\httpclient\Client;

/**
 * Сервис для работы с Telegram API
 */
class TelegramService extends Component
{
    /**
     * @var string $botToken Токен бота
     */
    public $botToken;

    /**
     * @var string $apiUrl URL Telegram API
     */
    public $apiUrl = 'https://api.telegram.org/bot';

    /**
     * @var Client $httpClient HTTP клиент
     */
    private $httpClient;

    /**
     * {@inheritdoc}
     */
    public function init()
    {
        parent::init();
        $this->httpClient = new Client();
    }

    /**
     * Отправка сообщения
     * @param string $chatId
     * @param string $text
     * @param array $keyboard
     * @return array
     */
    public function sendMessage($chatId, $text, $keyboard = null)
    {
        $data = [
            'chat_id' => $chatId,
            'text' => $text,
            'parse_mode' => 'HTML'
        ];

        if ($keyboard) {
            $data['reply_markup'] = json_encode($keyboard);
        }

        return $this->makeRequest('sendMessage', $data);
    }

    /**
     * Отправка фото
     * @param string $chatId
     * @param string $photo
     * @param string $caption
     * @return array
     */
    public function sendPhoto($chatId, $photo, $caption = '')
    {
        $data = [
            'chat_id' => $chatId,
            'photo' => $photo,
            'caption' => $caption,
            'parse_mode' => 'HTML'
        ];

        return $this->makeRequest('sendPhoto', $data);
    }

    /**
     * Выполнение запроса к API
     * @param string $method
     * @param array $data
     * @return array
     */
    public function makeRequest($method, $data)
    {
        $url = $this->apiUrl . $this->botToken . '/' . $method;

        try {
            $response = $this->httpClient
                ->createRequest()
                ->setMethod('POST')
                ->setUrl($url)
                ->setData($data)
                ->send();

            return $response->getData();
        } catch (\Exception $e) {
            \Yii::error('Telegram API error: ' . $e->getMessage(), 'telegram');
            return ['ok' => false, 'error' => $e->getMessage()];
        }
    }
} 