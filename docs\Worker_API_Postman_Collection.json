{"info": {"name": "Worker API Collection", "description": "API тесты для модуля Worker", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost/ish_top", "type": "string"}, {"key": "test_phone", "value": "+998901234567", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "1. Send SMS Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Accept-Language", "value": "ru"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"{{test_phone}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/worker/auth/send-code", "host": ["{{base_url}}"], "path": ["worker", "auth", "send-code"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has success field\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData.success).to.be.true;", "});", "", "pm.test(\"Response contains phone and expires_in\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('phone');", "    pm.expect(jsonData.data).to.have.property('expires_in');", "});"], "type": "text/javascript"}}]}, {"name": "2. <PERSON><PERSON> with Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Accept-Language", "value": "ru"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"{{test_phone}}\",\n  \"code\": \"1234\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/worker/auth/login", "host": ["{{base_url}}"], "path": ["worker", "auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains access_token\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('access_token');", "    ", "    // Сохраняем токен в переменной окружения", "    pm.environment.set(\"access_token\", jsonData.data.access_token);", "});", "", "pm.test(\"Response contains worker data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('worker');", "    pm.expect(jsonData.data.worker).to.have.property('id');", "    ", "    // Сохраняем ID работника", "    pm.environment.set(\"worker_id\", jsonData.data.worker.id);", "});"], "type": "text/javascript"}}]}, {"name": "3. <PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Accept-Language", "value": "ru"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/worker/auth/verify", "host": ["{{base_url}}"], "path": ["worker", "auth", "verify"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Token is valid\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.valid).to.be.true;", "});"], "type": "text/javascript"}}]}, {"name": "4. <PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Accept-Language", "value": "ru"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/worker/auth/refresh", "host": ["{{base_url}}"], "path": ["worker", "auth", "refresh"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Token refreshed successfully\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('access_token');", "    ", "    // Обновляем токен", "    pm.environment.set(\"access_token\", jsonData.data.access_token);", "});"], "type": "text/javascript"}}]}, {"name": "5. <PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Accept-Language", "value": "ru"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/worker/auth/logout", "host": ["{{base_url}}"], "path": ["worker", "auth", "logout"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Logout successful\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}]}]}, {"name": "Profile", "item": [{"name": "1. View Profile", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Accept-Language", "value": "ru"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/worker/profile/index", "host": ["{{base_url}}"], "path": ["worker", "profile", "index"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Profile retrieved successfully\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('worker');", "    pm.expect(jsonData.data.worker).to.have.property('phone');", "});"], "type": "text/javascript"}}]}, {"name": "2. Update Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Accept-Language", "value": "ru"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Тестовый Пользователь\",\n  \"age\": 25,\n  \"about\": \"Описание профиля тестового пользователя\",\n  \"profession_ids\": [1, 2]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/worker/profile/update", "host": ["{{base_url}}"], "path": ["worker", "profile", "update"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Profile updated successfully\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.worker.name).to.eql(\"Тестовый Пользователь\");", "    pm.expect(jsonData.data.worker.age).to.eql(25);", "});"], "type": "text/javascript"}}]}, {"name": "3. Upload Audio", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Accept-Language", "value": "ru"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "audio", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/worker/profile/upload-audio", "host": ["{{base_url}}"], "path": ["worker", "profile", "upload-audio"]}}}]}, {"name": "Vacancies", "item": [{"name": "1. Get Professions List", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Accept-Language", "value": "ru"}], "url": {"raw": "{{base_url}}/worker/vacancy/professions", "host": ["{{base_url}}"], "path": ["worker", "vacancy", "professions"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Professions list retrieved\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.be.an('array');", "    ", "    if (jsonData.data.length > 0) {", "        pm.expect(jsonData.data[0]).to.have.property('id');", "        pm.expect(jsonData.data[0]).to.have.property('name_ru');", "    }", "});"], "type": "text/javascript"}}]}, {"name": "2. Get Vacancies List", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Accept-Language", "value": "ru"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/worker/vacancy/list?page=1&per_page=10", "host": ["{{base_url}}"], "path": ["worker", "vacancy", "list"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "10"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Vacancies list retrieved\", function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('items');", "    pm.expect(jsonData.data).to.have.property('total');", "    pm.expect(jsonData.data).to.have.property('page');", "});"], "type": "text/javascript"}}]}, {"name": "3. Search Vacancies", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Accept-Language", "value": "ru"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/worker/vacancy/search?query=программист&page=1&per_page=10", "host": ["{{base_url}}"], "path": ["worker", "vacancy", "search"], "query": [{"key": "query", "value": "программист"}, {"key": "page", "value": "1"}, {"key": "per_page", "value": "10"}]}}}, {"name": "4. Get Vacancy Details", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Accept-Language", "value": "ru"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/worker/vacancy/detail/1", "host": ["{{base_url}}"], "path": ["worker", "vacancy", "detail", "1"]}}}]}, {"name": "Favorites", "item": [{"name": "1. Add to Favorites", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Accept-Language", "value": "ru"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"vacancy_id\": 1\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/worker/profile/add-favorite", "host": ["{{base_url}}"], "path": ["worker", "profile", "add-favorite"]}}}, {"name": "2. Get Favorites List", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Accept-Language", "value": "ru"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/worker/profile/favorites?page=1&per_page=10", "host": ["{{base_url}}"], "path": ["worker", "profile", "favorites"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "10"}]}}}, {"name": "3. <PERSON><PERSON><PERSON> from Favorites", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Accept-Language", "value": "ru"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"vacancy_id\": 1\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/worker/profile/remove-favorite", "host": ["{{base_url}}"], "path": ["worker", "profile", "remove-favorite"]}}}]}, {"name": "Error Testing", "item": [{"name": "Test Unauthorized Access", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Accept-Language", "value": "ru"}], "url": {"raw": "{{base_url}}/worker/profile/index", "host": ["{{base_url}}"], "path": ["worker", "profile", "index"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Should return 401 Unauthorized\", function () {", "    pm.response.to.have.status(401);", "});"], "type": "text/javascript"}}]}, {"name": "Test Validation Error", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Accept-Language", "value": "ru"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/worker/auth/send-code", "host": ["{{base_url}}"], "path": ["worker", "auth", "send-code"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Should return validation error\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([400, 422]);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.false;", "});"], "type": "text/javascript"}}]}]}], "event": [{"listen": "prerequest", "script": {"exec": ["// Общий Pre-request Script", "console.log('Request to:', pm.request.url.toString());"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// Общие тесты для всех запросов", "pm.test(\"Response time is acceptable\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test(\"Response is JSON\", function () {", "    pm.response.to.be.json;", "});", "", "if (pm.response.code !== 401 && pm.response.code !== 404) {", "    pm.test(\"Response has required structure\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('success');", "        pm.expect(jsonData).to.have.property('data');", "        pm.expect(jsonData).to.have.property('message');", "    });", "}"], "type": "text/javascript"}}]}