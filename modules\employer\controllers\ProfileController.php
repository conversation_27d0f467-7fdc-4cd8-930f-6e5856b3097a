<?php

namespace app\modules\employer\controllers;

use app\modules\employer\services\EmployerService;
use app\modules\employer\services\LoggingService;
use app\modules\employer\models\Employer;
use yii\web\Response;

/**
 * Контроллер для управления профилем работодателя
 * Все методы требуют авторизации
 */
class ProfileController extends BaseApiController
{
    /**
     * @var EmployerService
     */
    private $employerService;

    /**
     * @var LoggingService
     */
    private $loggingService;

    /**
     * {@inheritdoc}
     */
    public function init()
    {
        parent::init();
        $this->employerService = new EmployerService();
        $this->loggingService = new LoggingService();
    }

    /**
     * Просмотр профиля работодателя
     *
     * GET /employer/profile/view
     *
     * @return Response
     */
    public function actionView()
    {
        $employer = $this->requireAuth();

        try {
            $profile = $this->employerService->getEmployerProfileById($employer->id);

            if (!$profile) {
                return $this->sendNotFound($this->t('app', 'Profile not found'));
            }

            // Добавляем дополнительную статистику
            $profile['statistics'] = [
                'vacancies_count' => $this->employerService->getVacanciesCount($employer->id),
                'favorites_count' => $this->employerService->getFavoritesCount($employer->id),
            ];

            // Логируем просмотр профиля
            $this->loggingService->logProfileView($employer);

            return $this->sendSuccess($profile, $this->t('app', 'Profile retrieved successfully'));

        } catch (\Exception $e) {
            \Yii::error('Error retrieving employer profile: ' . $e->getMessage(), __METHOD__);
            return $this->sendError(
                $this->t('app', 'Failed to retrieve profile'),
                null,
                500
            );
        }
    }

    /**
     * Обновление профиля работодателя
     *
     * PUT /employer/profile/update
     * или
     * POST /employer/profile/update
     *
     * @return Response
     */
    public function actionUpdate()
    {
        $employer = $this->requireAuth();
        $profileData = $this->getRequestData();

        // Удаляем поля, которые нельзя обновлять через этот метод
        unset($profileData['id'], $profileData['phone'], $profileData['auth_token'], 
              $profileData['token_expires_at'], $profileData['created_at'], $profileData['deleted_at']);

        // Валидация данных
        $errors = $this->validateProfileData($profileData);
        if (!empty($errors)) {
            return $this->sendValidationError($errors);
        }

        try {
            $oldData = $this->employerService->getEmployerProfileById($employer->id);
            
            $success = $this->employerService->updateEmployerProfile($employer->id, $profileData);

            if (!$success) {
                return $this->sendError(
                    $this->t('app', 'Failed to update profile'),
                    null,
                    500
                );
            }

            // Получаем обновленные данные
            $updatedProfile = $this->employerService->getEmployerProfileById($employer->id);

            // Определяем какие поля были изменены
            $changedFields = $this->getChangedFields($oldData, $updatedProfile);

            // Логируем обновление профиля
            $this->loggingService->logProfileUpdate($employer, $changedFields);

            return $this->sendSuccess($updatedProfile, $this->t('app', 'Profile updated successfully'));

        } catch (\Exception $e) {
            \Yii::error('Error updating employer profile: ' . $e->getMessage(), __METHOD__);
            return $this->sendError(
                $this->t('app', 'Failed to update profile'),
                null,
                500
            );
        }
    }

    /**
     * Смена языка интерфейса
     *
     * POST /employer/profile/change-language
     *
     * @return Response
     */
    public function actionChangeLanguage()
    {
        $employer = $this->requireAuth();
        $language = $this->getRequestData('language');

        if (empty($language)) {
            return $this->sendValidationError([
                'language' => [$this->t('app', 'Language is required')]
            ]);
        }

        $allowedLanguages = [Employer::LANGUAGE_UZ, Employer::LANGUAGE_RU, Employer::LANGUAGE_EN];
        if (!in_array($language, $allowedLanguages)) {
            return $this->sendValidationError([
                'language' => [$this->t('app', 'Invalid language. Allowed: uz, ru, en')]
            ]);
        }

        try {
            $success = $this->employerService->changeLanguage($employer->id, $language);

            if (!$success) {
                return $this->sendError(
                    $this->t('app', 'Failed to change language'),
                    null,
                    500
                );
            }

            // Логируем смену языка
            $this->loggingService->logAction(
                $employer->id,
                'language_changed',
                ['new_language' => $language, 'old_language' => $employer->language]
            );

            return $this->sendSuccess([
                'language' => $language
            ], $this->t('app', 'Language changed successfully'));

        } catch (\Exception $e) {
            \Yii::error('Error changing language: ' . $e->getMessage(), __METHOD__);
            return $this->sendError(
                $this->t('app', 'Failed to change language'),
                null,
                500
            );
        }
    }

    /**
     * Получение доступных языков
     *
     * GET /employer/profile/languages
     *
     * @return Response
     */
    public function actionLanguages()
    {
        return $this->sendSuccess([
            'languages' => [
                [
                    'code' => Employer::LANGUAGE_UZ,
                    'name' => 'O\'zbek',
                    'native_name' => 'O\'zbek'
                ],
                [
                    'code' => Employer::LANGUAGE_RU,
                    'name' => 'Русский',
                    'native_name' => 'Русский'
                ],
                [
                    'code' => Employer::LANGUAGE_EN,
                    'name' => 'English',
                    'native_name' => 'English'
                ]
            ],
            'default' => Employer::LANGUAGE_UZ
        ]);
    }

    /**
     * Удаление аккаунта (мягкое удаление)
     *
     * POST /employer/profile/delete
     *
     * @return Response
     */
    public function actionDelete()
    {
        $employer = $this->requireAuth();
        $confirmPassword = $this->getRequestData('confirm_deletion');

        // Простая проверка подтверждения удаления
        if ($confirmPassword !== 'DELETE') {
            return $this->sendValidationError([
                'confirm_deletion' => [$this->t('app', 'Type DELETE to confirm account deletion')]
            ]);
        }

        try {
            // Мягкое удаление
            $employer->deleted_at = date('Y-m-d H:i:s');
            $employer->auth_token = null;
            $employer->token_expires_at = null;
            
            if (!$employer->save(false)) {
                return $this->sendError(
                    $this->t('app', 'Failed to delete account'),
                    null,
                    500
                );
            }

            // Логируем удаление аккаунта
            $this->loggingService->logAction(
                $employer->id,
                'account_deleted',
                ['deleted_at' => $employer->deleted_at]
            );

            return $this->sendSuccess(null, $this->t('app', 'Account deleted successfully'));

        } catch (\Exception $e) {
            \Yii::error('Error deleting employer account: ' . $e->getMessage(), __METHOD__);
            return $this->sendError(
                $this->t('app', 'Failed to delete account'),
                null,
                500
            );
        }
    }

    /**
     * Валидация данных профиля
     *
     * @param array $data
     * @return array
     */
    private function validateProfileData($data)
    {
        $errors = [];

        // Валидация имени
        if (isset($data['name'])) {
            if (empty(trim($data['name']))) {
                $errors['name'] = [$this->t('app', 'Name cannot be empty')];
            } elseif (mb_strlen($data['name']) > 100) {
                $errors['name'] = [$this->t('app', 'Name is too long (maximum 100 characters)')];
            }
        }

        // Валидация названия компании
        if (isset($data['business_name'])) {
            if (empty(trim($data['business_name']))) {
                $errors['business_name'] = [$this->t('app', 'Business name cannot be empty')];
            } elseif (mb_strlen($data['business_name']) > 255) {
                $errors['business_name'] = [$this->t('app', 'Business name is too long (maximum 255 characters)')];
            }
        }

        // Валидация ИНН
        if (isset($data['business_inn']) && !empty($data['business_inn'])) {
            if (!preg_match('/^\d{9}$/', $data['business_inn'])) {
                $errors['business_inn'] = [$this->t('app', 'Business INN must be 9 digits')];
            } else {
                // Проверяем уникальность ИНН
                $existingEmployer = Employer::find()
                    ->where(['business_inn' => $data['business_inn']])
                    ->andWhere(['!=', 'id', $this->getCurrentEmployer()->id])
                    ->andWhere(['deleted_at' => null])
                    ->exists();
                
                if ($existingEmployer) {
                    $errors['business_inn'] = [$this->t('app', 'This business INN is already registered')];
                }
            }
        }

        // Валидация адреса
        if (isset($data['business_address']) && !empty($data['business_address'])) {
            if (mb_strlen($data['business_address']) > 255) {
                $errors['business_address'] = [$this->t('app', 'Business address is too long (maximum 255 characters)')];
            }
        }

        // Валидация языка
        if (isset($data['language'])) {
            $allowedLanguages = [Employer::LANGUAGE_UZ, Employer::LANGUAGE_RU, Employer::LANGUAGE_EN];
            if (!in_array($data['language'], $allowedLanguages)) {
                $errors['language'] = [$this->t('app', 'Invalid language')];
            }
        }

        return $errors;
    }

    /**
     * Определение измененных полей
     *
     * @param array $oldData
     * @param array $newData
     * @return array
     */
    private function getChangedFields($oldData, $newData)
    {
        $changed = [];
        $fieldsToCheck = ['name', 'business_name', 'business_inn', 'business_address', 'language'];

        foreach ($fieldsToCheck as $field) {
            if (($oldData[$field] ?? null) !== ($newData[$field] ?? null)) {
                $changed[] = $field;
            }
        }

        return $changed;
    }
}
