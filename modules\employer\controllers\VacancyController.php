<?php

namespace app\modules\employer\controllers;

use app\modules\employer\services\EmployerService;
use app\modules\employer\services\LoggingService;
use app\modules\employer\models\Vacancy;
use app\modules\worker\models\Profession;
use yii\web\Response;
use yii\db\ActiveQuery;

/**
 * Контроллер управления вакансиями работодателя
 * Все методы требуют авторизации
 */
class VacancyController extends BaseApiController
{
    /**
     * @var EmployerService
     */
    private $employerService;

    /**
     * @var LoggingService
     */
    private $loggingService;

    /**
     * {@inheritdoc}
     */
    public function init()
    {
        parent::init();
        $this->employerService = new EmployerService();
        $this->loggingService = new LoggingService();
    }

    /**
     * Список вакансий работодателя
     *
     * GET /employer/vacancy/list
     *
     * @return Response
     */
    public function actionList()
    {
        $employer = $this->requireAuth();
        
        $page = max(1, (int)$this->getRequestData('page', 1));
        $limit = min(50, max(1, (int)$this->getRequestData('limit', 20)));
        $status = $this->getRequestData('status');

        try {
            $query = Vacancy::find()
                ->where(['employer_id' => $employer->id])
                ->andWhere(['deleted_at' => null])
                ->with(['profession'])
                ->orderBy(['created_at' => SORT_DESC]);

            // Фильтр по статусу
            if (!empty($status)) {
                $query->andWhere(['status' => $status]);
            }

            $totalCount = $query->count();
            $vacancies = $query
                ->offset(($page - 1) * $limit)
                ->limit($limit)
                ->all();

            $result = [
                'vacancies' => array_map(function($vacancy) {
                    return [
                        'id' => $vacancy->id,
                        'title' => $vacancy->title,
                        'description' => $vacancy->description,
                        'profession_id' => $vacancy->profession_id,
                        'profession' => $vacancy->profession ? [
                            'id' => $vacancy->profession->id,
                            'name_uz' => $vacancy->profession->name_uz,
                            'name_ru' => $vacancy->profession->name_ru,
                            'name_en' => $vacancy->profession->name_en,
                        ] : null,
                        'salary' => $vacancy->salary,
                        'status' => $vacancy->status,
                        'status_label' => $this->getStatusLabel($vacancy->status),
                        'latitude' => $vacancy->lat,
                        'longitude' => $vacancy->long,
                        'created_at' => $vacancy->created_at,
                    ];
                }, $vacancies),
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total_count' => $totalCount,
                    'page_count' => ceil($totalCount / $limit),
                ]
            ];

            return $this->sendSuccess($result, $this->t('app', 'Vacancies retrieved successfully'));

        } catch (\Exception $e) {
            \Yii::error('Error retrieving vacancies: ' . $e->getMessage(), __METHOD__);
            return $this->sendError(
                $this->t('app', 'Failed to retrieve vacancies'),
                null,
                500
            );
        }
    }

    /**
     * Детали вакансии
     *
     * GET /employer/vacancy/view?id=123
     *
     * @return Response
     */
    public function actionView()
    {
        $employer = $this->requireAuth();
        $vacancyId = (int)$this->getRequestData('id');

        if (empty($vacancyId)) {
            return $this->sendValidationError([
                'id' => [$this->t('app', 'Vacancy ID is required')]
            ]);
        }

        try {
            $vacancy = Vacancy::find()
                ->where(['id' => $vacancyId, 'employer_id' => $employer->id])
                ->andWhere(['deleted_at' => null])
                ->with(['profession'])
                ->one();

            if (!$vacancy) {
                return $this->sendNotFound($this->t('app', 'Vacancy not found'));
            }

            $result = [
                'id' => $vacancy->id,
                'title' => $vacancy->title,
                'description' => $vacancy->description,
                'profession_id' => $vacancy->profession_id,
                'profession' => $vacancy->profession ? [
                    'id' => $vacancy->profession->id,
                    'name_uz' => $vacancy->profession->name_uz,
                    'name_ru' => $vacancy->profession->name_ru,
                    'name_en' => $vacancy->profession->name_en,
                ] : null,
                'salary' => $vacancy->salary,
                'status' => $vacancy->status,
                'status_label' => $this->getStatusLabel($vacancy->status),
                'latitude' => $vacancy->lat,
                'longitude' => $vacancy->long,
                'created_at' => $vacancy->created_at,
            ];

            return $this->sendSuccess($result, $this->t('app', 'Vacancy retrieved successfully'));

        } catch (\Exception $e) {
            \Yii::error('Error retrieving vacancy: ' . $e->getMessage(), __METHOD__);
            return $this->sendError(
                $this->t('app', 'Failed to retrieve vacancy'),
                null,
                500
            );
        }
    }

    /**
     * Создание новой вакансии
     *
     * POST /employer/vacancy/create
     *
     * @return Response
     */
    public function actionCreate()
    {
        $employer = $this->requireAuth();
        $vacancyData = $this->getRequestData();

        // Валидация данных
        $errors = $this->validateVacancyData($vacancyData);
        if (!empty($errors)) {
            return $this->sendValidationError($errors);
        }

        try {
            $vacancy = new Vacancy();
            $vacancy->employer_id = $employer->id;
            $vacancy->title = $vacancyData['title'];
            $vacancy->description = $vacancyData['description'] ?? '';
            $vacancy->profession_id = $vacancyData['profession_id'];
            
            // Преобразуем salary_from и salary_to в одно поле salary
            $salaryFrom = $vacancyData['salary_from'] ?? null;
            $salaryTo = $vacancyData['salary_to'] ?? null;
            if ($salaryFrom && $salaryTo) {
                $vacancy->salary = $salaryFrom . '-' . $salaryTo;
            } elseif ($salaryFrom) {
                $vacancy->salary = 'от ' . $salaryFrom;
            } elseif ($salaryTo) {
                $vacancy->salary = 'до ' . $salaryTo;
            } else {
                $vacancy->salary = null;
            }
            
            // Используем существующие поля lat и long вместо latitude и longitude
            $vacancy->lat = $vacancyData['latitude'] ?? null;
            $vacancy->long = $vacancyData['longitude'] ?? null;
            
            // Определяем статус (в базе это integer)
            $statusValue = 1; // active по умолчанию
            if (isset($vacancyData['status'])) {
                switch ($vacancyData['status']) {
                    case 'active':
                        $statusValue = 1;
                        break;
                    case 'paused':
                        $statusValue = 2;
                        break;
                    case 'closed':
                        $statusValue = 3;
                        break;
                }
            }
            $vacancy->status = $statusValue;

            if (!$vacancy->save()) {
                return $this->sendValidationError($vacancy->getErrors());
            }

            // Загружаем связанные данные для ответа
            $vacancy->refresh();
            $vacancy = Vacancy::find()
                ->where(['id' => $vacancy->id])
                ->with(['profession'])
                ->one();

            $result = [
                'id' => $vacancy->id,
                'title' => $vacancy->title,
                'description' => $vacancy->description,
                'profession_id' => $vacancy->profession_id,
                'profession' => $vacancy->profession ? [
                    'id' => $vacancy->profession->id,
                    'name_uz' => $vacancy->profession->name_uz,
                    'name_ru' => $vacancy->profession->name_ru,
                    'name_en' => $vacancy->profession->name_en,
                ] : null,
                'salary' => $vacancy->salary,
                'status' => $vacancy->status,
                'status_label' => $this->getStatusLabel($vacancy->status),
                'latitude' => $vacancy->lat,
                'longitude' => $vacancy->long,
                'created_at' => $vacancy->created_at,
            ];

            // Логируем создание вакансии
            $this->loggingService->logAction(
                $employer->id,
                'vacancy_created',
                ['vacancy_id' => $vacancy->id, 'title' => $vacancy->title]
            );

            return $this->sendSuccess($result, $this->t('app', 'Vacancy created successfully'));

        } catch (\Exception $e) {
            \Yii::error('Error creating vacancy: ' . $e->getMessage(), __METHOD__);
            return $this->sendError(
                $this->t('app', 'Failed to create vacancy'),
                null,
                500
            );
        }
    }

    /**
     * Обновление вакансии
     *
     * PUT /employer/vacancy/update
     * или
     * POST /employer/vacancy/update
     *
     * @return Response
     */
    public function actionUpdate()
    {
        $employer = $this->requireAuth();
        $vacancyData = $this->getRequestData();
        $vacancyId = (int)($vacancyData['id'] ?? 0);

        if (empty($vacancyId)) {
            return $this->sendValidationError([
                'id' => [$this->t('app', 'Vacancy ID is required')]
            ]);
        }

        // Валидация данных
        $errors = $this->validateVacancyData($vacancyData, $vacancyId);
        if (!empty($errors)) {
            return $this->sendValidationError($errors);
        }

        try {
            $vacancy = Vacancy::find()
                ->where(['id' => $vacancyId, 'employer_id' => $employer->id])
                ->andWhere(['deleted_at' => null])
                ->one();

            if (!$vacancy) {
                return $this->sendNotFound($this->t('app', 'Vacancy not found'));
            }

            // Сохраняем старые данные для логирования
            $oldData = $vacancy->attributes;

            // Обновляем поля
            if (isset($vacancyData['title'])) {
                $vacancy->title = $vacancyData['title'];
            }
            if (isset($vacancyData['description'])) {
                $vacancy->description = $vacancyData['description'];
            }
            if (isset($vacancyData['profession_id'])) {
                $vacancy->profession_id = $vacancyData['profession_id'];
            }
            
            // Обновляем salary (объединяем salary_from и salary_to)
            if (isset($vacancyData['salary_from']) || isset($vacancyData['salary_to'])) {
                $salaryFrom = $vacancyData['salary_from'] ?? null;
                $salaryTo = $vacancyData['salary_to'] ?? null;
                if ($salaryFrom && $salaryTo) {
                    $vacancy->salary = $salaryFrom . '-' . $salaryTo;
                } elseif ($salaryFrom) {
                    $vacancy->salary = 'от ' . $salaryFrom;
                } elseif ($salaryTo) {
                    $vacancy->salary = 'до ' . $salaryTo;
                } else {
                    $vacancy->salary = null;
                }
            }
            
            // Обновляем статус (преобразуем строку в число)
            if (isset($vacancyData['status'])) {
                $statusValue = 1; // active по умолчанию
                switch ($vacancyData['status']) {
                    case 'active':
                        $statusValue = 1;
                        break;
                    case 'paused':
                        $statusValue = 2;
                        break;
                    case 'closed':
                        $statusValue = 3;
                        break;
                }
                $vacancy->status = $statusValue;
            }
            
            // Обновляем координаты (используем lat и long)
            if (isset($vacancyData['latitude'])) {
                $vacancy->lat = $vacancyData['latitude'];
            }
            if (isset($vacancyData['longitude'])) {
                $vacancy->long = $vacancyData['longitude'];
            }

            if (!$vacancy->save()) {
                return $this->sendValidationError($vacancy->getErrors());
            }

            // Загружаем обновленные данные с профессией
            $vacancy = Vacancy::find()
                ->where(['id' => $vacancy->id])
                ->with(['profession'])
                ->one();

            $result = [
                'id' => $vacancy->id,
                'title' => $vacancy->title,
                'description' => $vacancy->description,
                'profession_id' => $vacancy->profession_id,
                'profession' => $vacancy->profession ? [
                    'id' => $vacancy->profession->id,
                    'name_uz' => $vacancy->profession->name_uz,
                    'name_ru' => $vacancy->profession->name_ru,
                    'name_en' => $vacancy->profession->name_en,
                ] : null,
                'salary' => $vacancy->salary,
                'status' => $vacancy->status,
                'status_label' => $this->getStatusLabel($vacancy->status),
                'latitude' => $vacancy->lat,
                'longitude' => $vacancy->long,
                'created_at' => $vacancy->created_at,
            ];

            // Определяем измененные поля
            $changedFields = [];
            foreach ($vacancy->attributes as $field => $value) {
                if ($oldData[$field] != $value) {
                    $changedFields[] = $field;
                }
            }

            // Логируем обновление вакансии
            $this->loggingService->logAction(
                $employer->id,
                'vacancy_updated',
                [
                    'vacancy_id' => $vacancy->id,
                    'title' => $vacancy->title,
                    'changed_fields' => $changedFields
                ]
            );

            return $this->sendSuccess($result, $this->t('app', 'Vacancy updated successfully'));

        } catch (\Exception $e) {
            \Yii::error('Error updating vacancy: ' . $e->getMessage(), __METHOD__);
            return $this->sendError(
                $this->t('app', 'Failed to update vacancy'),
                null,
                500
            );
        }
    }

    /**
     * Удаление вакансии (мягкое удаление)
     *
     * DELETE /employer/vacancy/delete
     * или
     * POST /employer/vacancy/delete
     *
     * @return Response
     */
    public function actionDelete()
    {
        $employer = $this->requireAuth();
        $vacancyId = (int)$this->getRequestData('id');

        if (empty($vacancyId)) {
            return $this->sendValidationError([
                'id' => [$this->t('app', 'Vacancy ID is required')]
            ]);
        }

        try {
            $vacancy = Vacancy::find()
                ->where(['id' => $vacancyId, 'employer_id' => $employer->id])
                ->andWhere(['deleted_at' => null])
                ->one();

            if (!$vacancy) {
                return $this->sendNotFound($this->t('app', 'Vacancy not found'));
            }

            // Мягкое удаление
            $vacancy->deleted_at = date('Y-m-d H:i:s');
            
            if (!$vacancy->save(false)) {
                return $this->sendError(
                    $this->t('app', 'Failed to delete vacancy'),
                    null,
                    500
                );
            }

            // Логируем удаление вакансии
            $this->loggingService->logAction(
                $employer->id,
                'vacancy_deleted',
                ['vacancy_id' => $vacancy->id, 'title' => $vacancy->title]
            );

            return $this->sendSuccess(null, $this->t('app', 'Vacancy deleted successfully'));

        } catch (\Exception $e) {
            \Yii::error('Error deleting vacancy: ' . $e->getMessage(), __METHOD__);
            return $this->sendError(
                $this->t('app', 'Failed to delete vacancy'),
                null,
                500
            );
        }
    }

    /**
     * Получение доступных статусов вакансий
     *
     * GET /employer/vacancy/statuses
     *
     * @return Response
     */
    public function actionStatuses()
    {
        return $this->sendSuccess([
            'statuses' => [
                [
                    'value' => 'active',
                    'label_uz' => 'Faol',
                    'label_ru' => 'Активная',
                    'label_en' => 'Active'
                ],
                [
                    'value' => 'paused',
                    'label_uz' => 'To\'xtatilgan',
                    'label_ru' => 'Приостановлена',
                    'label_en' => 'Paused'
                ],
                [
                    'value' => 'closed',
                    'label_uz' => 'Yopilgan',
                    'label_ru' => 'Закрыта',
                    'label_en' => 'Closed'
                ]
            ]
        ]);
    }

    /**
     * Валидация данных вакансии
     *
     * @param array $data
     * @param int|null $vacancyId
     * @return array
     */
    private function validateVacancyData($data, $vacancyId = null)
    {
        $errors = [];

        // Валидация заголовка (только если поле присутствует или это создание новой вакансии)
        if ($vacancyId === null || isset($data['title'])) {
            if (empty(trim($data['title'] ?? ''))) {
                $errors['title'] = [$this->t('app', 'Title is required')];
            } elseif (mb_strlen($data['title']) > 255) {
                $errors['title'] = [$this->t('app', 'Title is too long (maximum 255 characters)')];
            }
        }

        // Валидация профессии (только если поле присутствует или это создание новой вакансии)
        if ($vacancyId === null || isset($data['profession_id'])) {
            if (empty($data['profession_id'])) {
                $errors['profession_id'] = [$this->t('app', 'Profession is required')];
            } else {
                $profession = Profession::find()
                    ->where(['id' => $data['profession_id']])
                    ->exists();
                
                if (!$profession) {
                    $errors['profession_id'] = [$this->t('app', 'Invalid profession')];
                }
            }
        }

        // Валидация зарплаты
        if (isset($data['salary_from']) && !empty($data['salary_from'])) {
            if (!is_numeric($data['salary_from']) || $data['salary_from'] < 0) {
                $errors['salary_from'] = [$this->t('app', 'Salary from must be a positive number')];
            }
        }

        if (isset($data['salary_to']) && !empty($data['salary_to'])) {
            if (!is_numeric($data['salary_to']) || $data['salary_to'] < 0) {
                $errors['salary_to'] = [$this->t('app', 'Salary to must be a positive number')];
            }
            
            if (!empty($data['salary_from']) && $data['salary_to'] < $data['salary_from']) {
                $errors['salary_to'] = [$this->t('app', 'Salary to must be greater than salary from')];
            }
        }

        // Валидация статуса
        if (isset($data['status']) && !in_array($data['status'], ['active', 'paused', 'closed'])) {
            $errors['status'] = [$this->t('app', 'Invalid status')];
        }

        // Валидация координат
        if (isset($data['latitude']) && !empty($data['latitude'])) {
            if (!is_numeric($data['latitude']) || $data['latitude'] < -90 || $data['latitude'] > 90) {
                $errors['latitude'] = [$this->t('app', 'Invalid latitude')];
            }
        }

        if (isset($data['longitude']) && !empty($data['longitude'])) {
            if (!is_numeric($data['longitude']) || $data['longitude'] < -180 || $data['longitude'] > 180) {
                $errors['longitude'] = [$this->t('app', 'Invalid longitude')];
            }
        }

        return $errors;
    }

    /**
     * Получить текстовую метку статуса
     *
     * @param int $status
     * @return string
     */
    private function getStatusLabel($status)
    {
        switch ($status) {
            case 1:
                return 'active';
            case 2:
                return 'paused';
            case 3:
                return 'closed';
            default:
                return 'unknown';
        }
    }
} 