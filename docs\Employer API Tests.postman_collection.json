{"info": {"_postman_id": "fb9d6444-99d4-4179-8c1e-a02e4baa9883", "name": "Employer API Tests", "description": "Collection for testing Employer module API endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "31088907"}, "item": [{"name": "Authentication", "item": [{"name": "Send SMS Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{phone}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/send-code", "host": ["{{base_url}}"], "path": ["auth", "send-code"]}}, "response": []}, {"name": "Login with Code", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    var jsonData = pm.response.json();", "    if (jsonData.success && jsonData.data && jsonData.data.auth_token) {", "        pm.collectionVariables.set('auth_token', jsonData.data.auth_token);", "    }", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{phone}}\",\n    \"code\": \"1234\"\n}"}, "url": {"raw": "{{base_url}}/employer/auth/verify-code", "host": ["{{base_url}}"], "path": ["employer", "auth", "verify-code"]}}, "response": []}, {"name": "Verify <PERSON>", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/auth/verify", "host": ["{{base_url}}"], "path": ["auth", "verify"]}}, "response": []}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/auth/logout", "host": ["{{base_url}}"], "path": ["auth", "logout"]}}, "response": []}]}, {"name": "Workers", "item": [{"name": "Public Worker List", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/employer/worker/list?page=1&limit=10", "host": ["{{base_url}}"], "path": ["employer", "worker", "list"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": []}, {"name": "Search Workers", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/employer/worker/search?query=програм&page=1&limit=10", "host": ["{{base_url}}"], "path": ["employer", "worker", "search"], "query": [{"key": "query", "value": "програм"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": []}, {"name": "Worker Detail (Requires Auth)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/employer/worker/detail?id=1", "host": ["{{base_url}}"], "path": ["employer", "worker", "detail"], "query": [{"key": "id", "value": "1"}]}}, "response": []}, {"name": "Get Professions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/employer/worker/professions", "host": ["{{base_url}}"], "path": ["employer", "worker", "professions"]}}, "response": []}]}, {"name": "Favorites", "item": [{"name": "Add to Favorites", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"worker_id\": 1\n}"}, "url": {"raw": "{{base_url}}/employer/favorite/add", "host": ["{{base_url}}"], "path": ["employer", "favorite", "add"]}}, "response": []}, {"name": "Remove from Favorites", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"worker_id\": 1\n}"}, "url": {"raw": "{{base_url}}/employer/favorite/remove", "host": ["{{base_url}}"], "path": ["employer", "favorite", "remove"]}}, "response": []}, {"name": "List Favorites", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/employer/favorite/list?page=1&limit=10", "host": ["{{base_url}}"], "path": ["employer", "favorite", "list"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": []}, {"name": "Toggle Favorite", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"worker_id\": 1\n}"}, "url": {"raw": "{{base_url}}/employer/favorite/toggle", "host": ["{{base_url}}"], "path": ["employer", "favorite", "toggle"]}}, "response": []}, {"name": "Favorites Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/employer/favorite/statistics", "host": ["{{base_url}}"], "path": ["employer", "favorite", "statistics"]}}, "response": []}]}, {"name": "Profile", "item": [{"name": "View Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/employer/profile/view", "host": ["{{base_url}}"], "path": ["employer", "profile", "view"]}}, "response": []}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"business_name\": \"My Company LLC\",\n    \"business_inn\": \"*********\",\n    \"business_address\": \"Tashkent, Uzbekistan\"\n}"}, "url": {"raw": "{{base_url}}/employer/profile/update", "host": ["{{base_url}}"], "path": ["employer", "profile", "update"]}}, "response": []}, {"name": "Change Language", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"language\": \"ru\"\n}"}, "url": {"raw": "{{base_url}}/employer/profile/change-language", "host": ["{{base_url}}"], "path": ["employer", "profile", "change-language"]}}, "response": []}, {"name": "Get Available Languages", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/employer/profile/languages", "host": ["{{base_url}}"], "path": ["employer", "profile", "languages"]}}, "response": []}]}, {"name": "Vacancies", "item": [{"name": "List Vacancies", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/employer/vacancy/list?page=1&limit=10", "host": ["{{base_url}}"], "path": ["employer", "vacancy", "list"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": []}, {"name": "View Vacancy", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/employer/vacancy/view?id=11", "host": ["{{base_url}}"], "path": ["employer", "vacancy", "view"], "query": [{"key": "id", "value": "11"}]}}, "response": []}, {"name": "Create Vacancy", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Oshpaz\",\n    \"description\": \"Job here\",\n    \"profession_id\": 1,\n    \"salary_from\": 5000,\n    \"salary_to\": 8000,\n    \"experience_from\": 3,\n    \"experience_to\": 5,\n    \"status\": \"active\",\n    \"location\": \"Andijon\",\n    \"latitude\": 41.2995,\n    \"longitude\": 69.2401\n}"}, "url": {"raw": "{{base_url}}/employer/vacancy/create", "host": ["{{base_url}}"], "path": ["employer", "vacancy", "create"]}}, "response": []}, {"name": "Update Vacancy", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"id\": 12,\n    \"title\": \"Updated Senior Developer\",\n    \"status\": \"paused\"\n}"}, "url": {"raw": "{{base_url}}/employer/vacancy/update", "host": ["{{base_url}}"], "path": ["employer", "vacancy", "update"]}}, "response": []}, {"name": "Delete Vacancy", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"id\": 12\n}"}, "url": {"raw": "{{base_url}}/employer/vacancy/delete", "host": ["{{base_url}}"], "path": ["employer", "vacancy", "delete"]}}, "response": []}, {"name": "Get Vacancy Statuses", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/employer/vacancy/statuses", "host": ["{{base_url}}"], "path": ["employer", "vacancy", "statuses"]}}, "response": []}]}], "variable": [{"key": "base_url", "value": "http://localhost/employer", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}, {"key": "phone", "value": "+998901234567", "type": "string"}]}