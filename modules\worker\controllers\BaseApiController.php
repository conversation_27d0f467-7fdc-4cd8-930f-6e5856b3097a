<?php

namespace app\modules\worker\controllers;

use yii\rest\Controller;
use yii\filters\ContentNegotiator;
use yii\filters\Cors;
use yii\web\Response;
use app\common\services\ApiResponse;

/**
 * Базовый API контроллер для модуля worker
 * Обеспечивает общую функциональность для всех API endpoints
 */
class BaseApiController extends Controller
{
    /**
     * @var bool Отключаем CSRF валидацию для API
     */
    public $enableCsrfValidation = false;

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        // Настройка CORS для мобильного приложения
        $behaviors['corsFilter'] = [
            'class' => Cors::class,
            'cors' => [
                'Origin' => ['*'],
                'Access-Control-Request-Method' => ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS'],
                'Access-Control-Request-Headers' => ['*'],
                'Access-Control-Allow-Credentials' => false,
                'Access-Control-Max-Age' => 86400,
            ],
        ];

        // Настройка формата ответа
        $behaviors['contentNegotiator'] = [
            'class' => ContentNegotiator::class,
            'formats' => [
                'application/json' => Response::FORMAT_JSON,
            ],
        ];

        return $behaviors;
    }

    /**
     * Отправить успешный ответ
     * 
     * @param mixed $data
     * @param string $message
     * @return Response
     */
    protected function sendSuccess($data = null, $message = '')
    {
        return ApiResponse::success($data, $message)->send();
    }

    /**
     * Отправить ответ с ошибкой
     * 
     * @param string $message
     * @param mixed $data
     * @param int $statusCode
     * @return Response
     */
    protected function sendError($message, $data = null, $statusCode = 400)
    {
        return ApiResponse::error($message, $data, $statusCode)->send();
    }

    /**
     * Отправить ответ "не найдено"
     * 
     * @param string $message
     * @return Response
     */
    protected function sendNotFound($message = 'Resource not found')
    {
        return ApiResponse::notFound($message)->send();
    }

    /**
     * Отправить ответ "не авторизован"
     * 
     * @param string $message
     * @return Response
     */
    protected function sendUnauthorized($message = 'Unauthorized')
    {
        return ApiResponse::unauthorized($message)->send();
    }

    /**
     * Отправить ответ с ошибкой валидации
     * 
     * @param array $errors
     * @param string $message
     * @return Response
     */
    protected function sendValidationError($errors, $message = 'Validation failed')
    {
        return ApiResponse::validationError($errors, $message)->send();
    }

    /**
     * Отправить ответ с пагинацией
     * 
     * @param array $items
     * @param int $total
     * @param int $page
     * @param int $perPage
     * @param string $message
     * @return Response
     */
    protected function sendPaginated($items, $total, $page, $perPage, $message = '')
    {
        return ApiResponse::paginated($items, $total, $page, $perPage, $message)->send();
    }

    /**
     * Получить язык из заголовков запроса
     * 
     * @return string
     */
    protected function getLanguage()
    {
        $acceptLanguage = \Yii::$app->request->headers->get('Accept-Language', 'ru');
        
        // Парсим заголовок Accept-Language
        $languages = explode(',', $acceptLanguage);
        $primaryLanguage = trim(explode(';', $languages[0])[0]);
        
        // Поддерживаемые языки
        $supportedLanguages = ['ru', 'uz', 'en'];
        
        // Проверяем точное совпадение
        if (in_array($primaryLanguage, $supportedLanguages)) {
            return $primaryLanguage;
        }
        
        // Проверяем по коду языка (например, ru-RU -> ru)
        $languageCode = explode('-', $primaryLanguage)[0];
        if (in_array($languageCode, $supportedLanguages)) {
            return $languageCode;
        }
        
        // По умолчанию русский
        return 'ru';
    }

    /**
     * Получить переведенное сообщение
     * 
     * @param string $category
     * @param string $message
     * @param array $params
     * @param string $language
     * @return string
     */
    protected function t($category, $message, $params = [], $language = null)
    {
        if ($language === null) {
            $language = $this->getLanguage();
        }
        
        return \Yii::t($category, $message, $params, $language);
    }

    /**
     * Проверить, требуется ли завершение профиля для данного действия
     *
     * @param string $action Название действия
     * @return bool
     */
    protected function requiresProfileCompletion($action)
    {
        // Действия, которые не требуют завершенного профиля
        $allowedActions = [
            'login',
            'send-code',
            'logout',
            'verify',
            'refresh',
            'index', // Просмотр профиля
            'update' // Обновление профиля
        ];

        return !in_array($action, $allowedActions);
    }

    /**
     * Проверить завершенность профиля worker'а
     *
     * @param \app\modules\worker\models\Worker $worker
     * @return Response|null Возвращает Response с ошибкой если профиль не завершен, иначе null
     */
    protected function checkProfileCompletion($worker)
    {
        if (!$worker->isProfileComplete() && $this->requiresProfileCompletion($this->action->id)) {
            return $this->sendError(
                $this->t('app', 'Profile completion required'),
                [
                    'profile_completion_required' => true,
                    'message' => $this->t('app', 'Please complete your profile to access this feature'),
                    'profile_status' => $worker->profile_status,
                    'completion_steps' => [
                        'name' => empty($worker->name),
                        'age' => empty($worker->age),
                        'about' => empty($worker->about),
                        'professions' => count($worker->professions) === 0
                    ]
                ],
                403
            );
        }

        return null;
    }
}
