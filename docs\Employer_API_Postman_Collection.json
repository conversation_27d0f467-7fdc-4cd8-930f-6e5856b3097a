{"info": {"name": "Employer API Tests", "description": "Collection for testing Employer module API endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost/employer", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}, {"key": "phone", "value": "+998901234567", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Send SMS Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{phone}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/send-code", "host": ["{{base_url}}"], "path": ["auth", "send-code"]}}}, {"name": "Login with Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{phone}}\",\n    \"code\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    var jsonData = pm.response.json();", "    if (jsonData.success && jsonData.data && jsonData.data.auth_token) {", "        pm.collectionVariables.set('auth_token', jsonData.data.auth_token);", "    }", "}"]}}]}, {"name": "Verify <PERSON>", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/auth/verify", "host": ["{{base_url}}"], "path": ["auth", "verify"]}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/auth/logout", "host": ["{{base_url}}"], "path": ["auth", "logout"]}}}]}, {"name": "Workers", "item": [{"name": "Public Worker List", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/worker/list?page=1&limit=10", "host": ["{{base_url}}"], "path": ["worker", "list"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Search Workers", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/worker/search?query=developer&page=1&limit=10", "host": ["{{base_url}}"], "path": ["worker", "search"], "query": [{"key": "query", "value": "developer"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Worker Detail (Requires Auth)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/worker/detail?worker_id=1", "host": ["{{base_url}}"], "path": ["worker", "detail"], "query": [{"key": "worker_id", "value": "1"}]}}}, {"name": "Get Professions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/worker/professions", "host": ["{{base_url}}"], "path": ["worker", "professions"]}}}]}, {"name": "Favorites", "item": [{"name": "Add to Favorites", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"worker_id\": 1\n}"}, "url": {"raw": "{{base_url}}/favorite/add", "host": ["{{base_url}}"], "path": ["favorite", "add"]}}}, {"name": "Remove from Favorites", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"worker_id\": 1\n}"}, "url": {"raw": "{{base_url}}/favorite/remove", "host": ["{{base_url}}"], "path": ["favorite", "remove"]}}}, {"name": "List Favorites", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/favorite/list?page=1&limit=10", "host": ["{{base_url}}"], "path": ["favorite", "list"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Toggle Favorite", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"worker_id\": 1\n}"}, "url": {"raw": "{{base_url}}/favorite/toggle", "host": ["{{base_url}}"], "path": ["favorite", "toggle"]}}}, {"name": "Favorites Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/favorite/statistics", "host": ["{{base_url}}"], "path": ["favorite", "statistics"]}}}]}, {"name": "Profile", "item": [{"name": "View Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/profile/view", "host": ["{{base_url}}"], "path": ["profile", "view"]}}}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"business_name\": \"My Company LLC\",\n    \"business_inn\": \"*********\",\n    \"business_address\": \"Tashkent, Uzbekistan\"\n}"}, "url": {"raw": "{{base_url}}/profile/update", "host": ["{{base_url}}"], "path": ["profile", "update"]}}}, {"name": "Change Language", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"language\": \"ru\"\n}"}, "url": {"raw": "{{base_url}}/profile/change-language", "host": ["{{base_url}}"], "path": ["profile", "change-language"]}}}, {"name": "Get Available Languages", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/profile/languages", "host": ["{{base_url}}"], "path": ["profile", "languages"]}}}]}, {"name": "Vacancies", "item": [{"name": "List Vacancies", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/vacancy/list?page=1&limit=10", "host": ["{{base_url}}"], "path": ["vacancy", "list"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "View Vacancy", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/vacancy/view?id=1", "host": ["{{base_url}}"], "path": ["vacancy", "view"], "query": [{"key": "id", "value": "1"}]}}}, {"name": "Create Vacancy", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Senior Developer\",\n    \"description\": \"Job description here\",\n    \"profession_id\": 1,\n    \"salary_from\": 1000,\n    \"salary_to\": 2000,\n    \"experience_from\": 3,\n    \"experience_to\": 5,\n    \"status\": \"active\",\n    \"location\": \"Tashkent\",\n    \"latitude\": 41.2995,\n    \"longitude\": 69.2401\n}"}, "url": {"raw": "{{base_url}}/vacancy/create", "host": ["{{base_url}}"], "path": ["vacancy", "create"]}}}, {"name": "Update Vacancy", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"id\": 1,\n    \"title\": \"Updated Senior Developer\",\n    \"status\": \"paused\"\n}"}, "url": {"raw": "{{base_url}}/vacancy/update", "host": ["{{base_url}}"], "path": ["vacancy", "update"]}}}, {"name": "Delete Vacancy", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"id\": 1\n}"}, "url": {"raw": "{{base_url}}/vacancy/delete", "host": ["{{base_url}}"], "path": ["vacancy", "delete"]}}}, {"name": "Get Vacancy Statuses", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/vacancy/statuses", "host": ["{{base_url}}"], "path": ["vacancy", "statuses"]}}}]}]}