<?php

use yii\db\Migration;

class m250715_050920_add_test_workers extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Тестовый пользователь #1 - ПОЛНЫЙ ПРОФИЛЬ
        $this->insert('{{%workers}}', [
            'name' => 'Тестовый Работник',
            'phone' => '+998901111111',
            'age' => 28,
            'about' => 'Опытный программист с 5-летним стажем. Работаю с PHP, JavaScript, Python. Готов к командировкам.',
            'lat' => 41.311081,  // Координаты Ташкента
            'long' => 69.240562,
            'experience_years' => 5,
            'language' => 'ru',
            'status' => 1,
            'profile_status' => 1, // Полный профиль
            'created_at' => date('Y-m-d H:i:s'),
        ]);

        // Получаем ID первого тестового работника
        $worker1Id = $this->db->getLastInsertID();

        // Привязываем профессии к первому работнику (Программист и Менеджер)
        $this->batchInsert('{{%worker_professions}}', ['worker_id', 'profession_id'], [
            [$worker1Id, 16], // Программист
            [$worker1Id, 17], // Менеджер
        ]);

        // Тестовый пользователь #2 - МИНИМАЛЬНЫЙ ПРОФИЛЬ (только телефон)
        $this->insert('{{%workers}}', [
            'phone' => '+998902222222',
            'language' => 'ru',
            'status' => 1,
            'profile_status' => 0, // Неполный профиль - нужно заполнить
            'created_at' => date('Y-m-d H:i:s'),
        ]);

        // Тестовый работодатель #1 - ПОЛНЫЙ ПРОФИЛЬ
        $this->insert('{{%employers}}', [
            'name' => 'Тестовый Работодатель',
            'phone' => '+998903333333',
            'business_name' => 'ООО "Тест Компания"',
            'business_inn' => '***********',
            'business_address' => 'г. Ташкент, ул. Тестовая, 10',
            'language' => 'ru',
            'status' => 1,
            'created_at' => date('Y-m-d H:i:s'),
        ]);

       

        echo "Test users created successfully:\n";
        echo "WORKERS:\n";
        echo "- Full profile: +998901111111 (code: 1234)\n";
        echo "- Minimal profile: +998902222222 (code: 1234)\n";
        echo "EMPLOYERS:\n";
        echo "- Full profile: +998903333333 (code: 1234)\n";
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Удаляем тестовых пользователей
        $this->delete('{{%worker_professions}}', ['worker_id' => [
            $this->db->createCommand('SELECT id FROM {{%workers}} WHERE phone IN (:phone1, :phone2)')
                ->bindValues([':phone1' => '+998901111111', ':phone2' => '+998902222222'])
                ->queryColumn()
        ]]);
        
        $this->delete('{{%workers}}', ['phone' => ['+998901111111', '+998902222222']]);
        
        // Удаляем тестовых работодателей
        $this->delete('{{%employers}}', ['phone' => ['+998903333333']]);

        echo "Test users removed successfully.\n";
        return true;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250715_050920_add_test_workers cannot be reverted.\n";

        return false;
    }
    */
}
