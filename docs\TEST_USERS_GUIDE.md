# Тестирование Worker API - Инструкция

## Что сделано

### 1. Исключения в коде для тестовых номеров
- **AuthController**: обход проверки SMS кода для тестовых номеров
- **SmsService**: имитация отправки SMS для тестовых номеров
- Работает только в `dev` и `test` окружениях

### 2. Миграция с тестовыми пользователями
**Файл**: `migrations/m250715_050920_add_test_workers.php`

Создает двух тестовых пользователей:

#### Пользователь #1 - ПОЛНЫЙ ПРОФИЛЬ
- **Телефон**: `+998901111111`
- **Код**: `1234`
- **Профиль**: полностью заполен
- **Профессии**: Программист, Менеджер
- **Статус**: готов к работе с вакансиями
- **Данные**: имя, возраст, описание, координаты

#### Пользователь #2 - МИНИМАЛЬНЫЙ ПРОФИЛЬ  
- **Телефон**: `+998902222222`
- **Код**: `1234`
- **Профиль**: только номер телефона
- **Статус**: нужно заполнить профиль
- **Использование**: для тестирования обновления профиля

### 3. Обновленные переменные Postman
- `test_phone_full` = `+998901111111`
- `test_phone_minimal` = `+998902222222`

## Как запустить

### 1. Запустить миграцию
```bash
cd d:\OSPanel\domains\ish_top
php yii migrate
```

### 2. Импортировать обновленные файлы в Postman
- `Worker_API_Environment.postman_environment.json` (обновлен)
- `Worker_API_Postman_Collection.json` (обновить если нужно)

## Сценарии тестирования

### Сценарий A: Полный профиль (готов к работе)
1. **Send SMS Code** с `+998901111111`
2. **Login** с кодом `1234`
3. **View Profile** - увидите полные данные
4. **Get Vacancies List** - должно работать без ошибок
5. **Search Vacancies** - полный доступ
6. **Add to Favorites** - функционал избранного

### Сценарий B: Минимальный профиль (нужно заполнить)
1. **Send SMS Code** с `+998902222222`
2. **Login** с кодом `1234`
3. **View Profile** - только телефон
4. **Get Vacancies List** - ошибка 403 "Profile completion required"
5. **Update Profile** - заполнить данные
6. **Get Vacancies List** - теперь должно работать

## Что тестировать

### ✅ Работает без SMS
- Отправка кода на тестовые номера
- Авторизация с кодом `1234`
- Все остальные функции API

### ✅ Профиль completion flow
- Ошибка 403 для неполного профиля
- Успешное обновление профиля
- Доступ после заполнения

### ✅ Полный функционал
- Просмотр и обновление профиля
- Список и поиск вакансий
- Работа с избранным

## Структура тестовых данных

### Полный профиль (+998901111111):
```json
{
  "name": "Тестовый Работник",
  "age": 28,
  "about": "Опытный программист с 5-летним стажем...",
  "experience_years": 5,
  "professions": ["Программист", "Менеджер"],
  "profile_status": 1,
  "coordinates": [41.311081, 69.240562]
}
```

### Минимальный профиль (+998902222222):
```json
{
  "phone": "+998902222222",
  "profile_status": 0,
  "name": null,
  "age": null,
  "about": null
}
```

## Проверка в Postman

### 1. Переменные окружения
Убедитесь, что выбрано окружение "Worker API Local" и установлены:
- `base_url`: ваш локальный URL
- `test_phone_full`: +998901111111
- `test_phone_minimal`: +998902222222

### 2. Последовательность запросов
1. Для полного профиля используйте `{{test_phone_full}}`
2. Для минимального профиля используйте `{{test_phone_minimal}}`
3. Всегда используйте код `1234`

### 3. Ожидаемые результаты
- **Полный профиль**: доступ ко всем функциям
- **Минимальный профиль**: ошибка 403 до обновления профиля

---

**Готово!** Теперь можно тестировать без настоящих SMS.
