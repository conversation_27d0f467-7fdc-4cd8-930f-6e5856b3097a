<?php

namespace app\common\enums;

/**
 * Enum для статусов вакансий
 */
enum VacancyStatus: int
{
    case DRAFT = 0;      // Черновик
    case ACTIVE = 1;     // Активная (опубликованная)
    case PAUSED = 2;     // Приостановлена
    case CLOSED = 3;     // Закрыта
    case ARCHIVED = 4;   // Архивная

    /**
     * Получить название статуса на русском
     * 
     * @return string
     */
    public function getLabel(): string
    {
        return match($this) {
            self::DRAFT => \Yii::t('app', 'Draft'),
            self::ACTIVE => \Yii::t('app', 'Active'),
            self::PAUSED => \Yii::t('app', 'Paused'), 
            self::CLOSED => \Yii::t('app', 'Closed'),
            self::ARCHIVED => \Yii::t('app', 'Archived'),
        };
    }

    /**
     * Получить цвет статуса для UI
     * 
     * @return string
     */
    public function getColor(): string
    {
        return match($this) {
            self::DRAFT => 'secondary',
            self::ACTIVE => 'success',
            self::PAUSED => 'warning',
            self::CLOSED => 'danger',
            self::ARCHIVED => 'dark',
        };
    }

    /**
     * Проверить, активна ли вакансия
     * 
     * @return bool
     */
    public function isActive(): bool
    {
        return $this === self::ACTIVE;
    }

    /**
     * Проверить, можно ли редактировать вакансию
     * 
     * @return bool
     */
    public function isEditable(): bool
    {
        return in_array($this, [self::DRAFT, self::ACTIVE, self::PAUSED]);
    }

    /**
     * Получить все активные статусы (видимые соискателям)
     * 
     * @return array
     */
    public static function getActiveStatuses(): array
    {
        return [self::ACTIVE];
    }

    /**
     * Получить все статусы для выбора в админке
     * 
     * @return array
     */
    public static function getAllForSelect(): array
    {
        $result = [];
        foreach (self::cases() as $status) {
            $result[$status->value] = $status->getLabel();
        }
        return $result;
    }
} 