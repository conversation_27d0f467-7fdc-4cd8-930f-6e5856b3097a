<?php

namespace app\modules\worker\services;

use app\modules\employer\models\Vacancy;
use app\common\enums\VacancyStatus;
use app\modules\worker\models\Profession;
use yii\data\Pagination;
use yii\db\Query;

/**
 * Сервис для работы с вакансиями
 */
class VacancyService
{
    /**
     * Получить список вакансий с пагинацией
     * 
     * @param int $page
     * @param int $perPage
     * @param array $filters
     * @return array
     */
    public function getVacanciesList($page = 1, $perPage = 20, $filters = [])
    {
        $query = Vacancy::find()
            ->with(['employer', 'profession'])
            ->where(['deleted_at' => null])
            ->andWhere(['status' => VacancyStatus::ACTIVE->value]); // Только активные вакансии

        // Применяем фильтры
        $this->applyFilters($query, $filters);

        // Подсчет общего количества
        $total = $query->count();

        // Пагинация
        $pagination = new Pagination([
            'totalCount' => $total,
            'page' => $page - 1, // Yii использует 0-based индексацию
            'pageSize' => $perPage,
        ]);

        // Получаем данные
        $vacancies = $query
            ->offset($pagination->offset)
            ->limit($pagination->limit)
            ->orderBy(['created_at' => SORT_DESC])
            ->all();

        // Форматируем данные
        $items = [];
        foreach ($vacancies as $vacancy) {
            $items[] = $this->formatVacancyForList($vacancy);
        }

        return [
            'items' => $items,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage
        ];
    }

    /**
     * Поиск вакансий
     * 
     * @param string $query
     * @param int $page
     * @param int $perPage
     * @param array $filters
     * @return array
     */
    public function searchVacancies($query, $page = 1, $perPage = 20, $filters = [])
    {
        $vacancyQuery = Vacancy::find()
            ->with(['employer', 'profession'])
            ->where(['deleted_at' => null])
            ->andWhere(['status' => VacancyStatus::ACTIVE->value]);

        // Поиск по тексту
        if (!empty($query)) {
            $vacancyQuery->andWhere([
                'or',
                ['ilike', 'title', $query],
                ['ilike', 'description', $query]
            ]);
        }

        // Применяем фильтры
        $this->applyFilters($vacancyQuery, $filters);

        // Подсчет общего количества
        $total = $vacancyQuery->count();

        // Пагинация
        $pagination = new Pagination([
            'totalCount' => $total,
            'page' => $page - 1,
            'pageSize' => $perPage,
        ]);

        // Получаем данные
        $vacancies = $vacancyQuery
            ->offset($pagination->offset)
            ->limit($pagination->limit)
            ->orderBy(['created_at' => SORT_DESC])
            ->all();

        // Форматируем данные
        $items = [];
        foreach ($vacancies as $vacancy) {
            $items[] = $this->formatVacancyForList($vacancy);
        }

        return [
            'items' => $items,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage
        ];
    }

    /**
     * Получить детальную информацию о вакансии
     * 
     * @param int $id
     * @return array|null
     */
    public function getVacancyDetail($id)
    {
        $vacancy = Vacancy::find()
            ->with(['employer', 'profession'])
            ->where(['id' => $id, 'deleted_at' => null, 'status' => VacancyStatus::ACTIVE->value])
            ->one();

        if (!$vacancy) {
            return null;
        }

        return $this->formatVacancyForDetail($vacancy);
    }

    /**
     * Применить фильтры к запросу
     * 
     * @param \yii\db\ActiveQuery $query
     * @param array $filters
     */
    private function applyFilters($query, $filters)
    {
        // Фильтр по профессии
        if (!empty($filters['profession_id'])) {
            $query->andWhere(['profession_id' => $filters['profession_id']]);
        }

        // Фильтр по зарплате (минимальная)
        if (!empty($filters['min_salary'])) {
            $query->andWhere(['>=', 'CAST(REGEXP_REPLACE(salary, \'[^0-9]\', \'\', \'g\') AS INTEGER)', $filters['min_salary']]);
        }

        // Фильтр по местоположению (радиус в км)
        if (!empty($filters['lat']) && !empty($filters['lng']) && !empty($filters['radius'])) {
            $lat = $filters['lat'];
            $lng = $filters['lng'];
            $radius = $filters['radius'];
            
            // Используем формулу Haversine для поиска в радиусе
            $query->andWhere([
                '<=',
                new \yii\db\Expression("
                    6371 * acos(
                        cos(radians(:lat)) * cos(radians(lat)) * 
                        cos(radians(long) - radians(:lng)) + 
                        sin(radians(:lat)) * sin(radians(lat))
                    )
                ", [':lat' => $lat, ':lng' => $lng]),
                $radius
            ]);
        }
    }

    /**
     * Форматировать вакансию для списка
     * 
     * @param Vacancy $vacancy
     * @return array
     */
    private function formatVacancyForList($vacancy)
    {
        return [
            'id' => $vacancy->id,
            'title' => $vacancy->title,
            'salary' => $vacancy->salary,
            'profession' => [
                'id' => $vacancy->profession->id,
                'name_ru' => $vacancy->profession->name_ru,
                'name_uz' => $vacancy->profession->name_uz,
                'name_en' => $vacancy->profession->name_en,
            ],
            'employer' => [
                'id' => $vacancy->employer->id,
                'business_name' => $vacancy->employer->business_name,
            ],
            'location' => [
                'lat' => $vacancy->lat,
                'lng' => $vacancy->long,
            ],
            'created_at' => $vacancy->created_at,
        ];
    }

    /**
     * Форматировать вакансию для детального просмотра
     * 
     * @param Vacancy $vacancy
     * @return array
     */
    private function formatVacancyForDetail($vacancy)
    {
        return [
            'id' => $vacancy->id,
            'title' => $vacancy->title,
            'description' => $vacancy->description,
            'salary' => $vacancy->salary,
            'profession' => [
                'id' => $vacancy->profession->id,
                'name_ru' => $vacancy->profession->name_ru,
                'name_uz' => $vacancy->profession->name_uz,
                'name_en' => $vacancy->profession->name_en,
            ],
            'employer' => [
                'id' => $vacancy->employer->id,
                'name' => $vacancy->employer->name,
                'business_name' => $vacancy->employer->business_name,
                'business_address' => $vacancy->employer->business_address,
                'phone' => $vacancy->employer->phone,
            ],
            'location' => [
                'lat' => $vacancy->lat,
                'lng' => $vacancy->long,
            ],
            'created_at' => $vacancy->created_at,
        ];
    }
}
