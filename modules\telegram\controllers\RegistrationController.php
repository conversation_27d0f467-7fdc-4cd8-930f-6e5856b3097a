<?php

namespace app\modules\telegram\controllers;

use yii\web\Controller;
use yii\web\Response;
use app\modules\telegram\models\TemporaryRegistration;
use app\modules\telegram\models\TelegramService;
use app\modules\worker\models\Profession;
use app\common\models\WorkerLog;

/**
 * Контроллер регистрации работника через Telegram
 */
class RegistrationController extends Controller
{
    /**
     * @var array Отключение CSRF
     */
    public $enableCsrfValidation = false;

    /**
     * @var TelegramService Сервис Telegram
     */
    private $telegramService;

    /**
     * {@inheritdoc}
     */
    public function init()
    {
        parent::init();
        $this->telegramService = \Yii::$app->get('telegramService');
    }

    /**
     * Получение переводимого сообщения
     */
    private function getMessage($key, $params = [], $language = null)
    {
        if ($language) {
            \Yii::$app->language = $language;
        }
        return \Yii::t('telegram', $key, $params);
    }
    
    /**
     * Получение правильного склонения слова "лет" с учетом языка
     */
    private function getYearsText($years, $language = 'ru')
    {
        \Yii::$app->language = $language;
        
        if ($language === 'ru') {
            if ($years % 10 == 1 && $years % 100 != 11) {
                return \Yii::t('telegram', 'years.1');
            } elseif (in_array($years % 10, [2, 3, 4]) && !in_array($years % 100, [12, 13, 14])) {
                return \Yii::t('telegram', 'years.2_4');
            } else {
                return \Yii::t('telegram', 'years.other');
            }
        } else {
            // Для uz и en просто возвращаем соответствующий перевод
            return \Yii::t('telegram', 'years.other');
        }
    }

    /**
     * Обработка этапов регистрации
     */
    public function actionProcess()
    {
        // Получаем данные из webhook или из WebhookController
        $update = \Yii::$app->params['currentUpdate'] ?? null;
        
        if (!$update) {
            // Если нет в params, пробуем получить напрямую
            $input = file_get_contents('php://input');
            $update = json_decode($input, true);
        }

        if (!$update) {
            return $this->asJson(['ok' => false]);
        }

        $chatId = $this->getChatId($update);
        if (!$chatId) {
            return $this->asJson(['ok' => false]);
        }

        // Получаем или создаем регистрацию
        $registration = TemporaryRegistration::findByChatId($chatId);
        if (!$registration) {
            $registration = TemporaryRegistration::createOrUpdate($chatId);
            if (!$registration) {
                // Если не удалось создать регистрацию, пробуем создать вручную
                $registration = new TemporaryRegistration();
                $registration->chat_id = $chatId;
                $registration->status = TemporaryRegistration::STEP_START;
                if (!$registration->save()) {
                    \Yii::error("Failed to create registration for chat_id: {$chatId}. Errors: " . json_encode($registration->errors), 'telegram');
                    return $this->asJson(['ok' => false, 'error' => 'registration_creation_failed']);
                }
            }
        }

        // Обрабатываем команду /start
        if (isset($update['message']['text']) && $update['message']['text'] === '/start') {
            return $this->handleStartCommand($chatId, $registration);
        }

        // Обрабатываем callback query (кнопки)
        if (isset($update['callback_query'])) {
            $callbackData = $update['callback_query']['data'];
            
            // Если это начало регистрации - запускаем процесс
            if ($callbackData === 'start_registration') {
                // Проверяем что registration валидный перед передачей в handleStart
                if (!$registration || !is_object($registration)) {
                    \Yii::error("Invalid registration object in callback start_registration for chat_id: {$chatId}", 'telegram');
                    return $this->asJson(['ok' => false, 'error' => 'invalid_registration_in_callback']);
                }
                return $this->handleStart($chatId, $registration);
            }
            
            return $this->handleCallback($update['callback_query'], $registration);
        }

        // Обрабатываем текстовое сообщение
        if (isset($update['message']['text'])) {
            return $this->handleTextMessage($update['message'], $registration);
        }

        // Обрабатываем контакт (номер телефона)
        if (isset($update['message']['contact'])) {
            return $this->handleContact($update['message'], $registration);
        }

        // Обрабатываем геолокацию
        if (isset($update['message']['location'])) {
            return $this->handleLocation($update['message'], $registration);
        }

        // Обрабатываем голосовые сообщения (только voice, не audio/document)
        if (isset($update['message']['voice'])) {
            return $this->handleVoice($update['message'], $registration);
        }

        // Отклоняем обычные аудиофайлы, музыку и документы
        if (isset($update['message']['audio']) || isset($update['message']['document'])) {
            return $this->handleInvalidAudio($update['message'], $registration);
        }

        // Отклоняем фото, видео, стикеры и другие медиафайлы
        if (isset($update['message']['photo']) || 
            isset($update['message']['video']) || 
            isset($update['message']['sticker']) ||
            isset($update['message']['animation']) ||
            isset($update['message']['video_note'])) {
            return $this->handleInvalidMedia($update['message'], $registration);
        }

        return $this->asJson(['ok' => true]);
    }

    /**
     * Обработка команды /start - показываем приветствие или начинаем регистрацию
     */
    private function handleStartCommand($chatId, $registration)
    {
        $worker = \app\modules\worker\models\Worker::findOne(['chat_id' => $chatId]);
        
        if ($worker && $worker->status == 1) {
            // Существующий пользователь - показываем приветствие
            // Устанавливаем язык для переводов
            \Yii::$app->language = $worker->language ?: 'ru';
            
            $text = $this->getMessage('welcome.back', ['name' => $worker->name]);
                   
            $this->telegramService->sendMessage($chatId, $text);
            
            // Логируем приветствие
            try {
                WorkerLog::logAction($chatId, 'welcome_existing_user', [
                    'worker_id' => $worker->id
                ], $worker->id);
            } catch (\Exception $e) {
                \Yii::error("Failed to log welcome: " . $e->getMessage(), 'telegram');
            }
            
        } else {
            // Новый пользователь или незавершенная регистрация - начинаем/продолжаем регистрацию
            // Проверяем что registration валидный перед передачей в handleStart
            if (!$registration || !is_object($registration)) {
                \Yii::error("Invalid registration object in handleStartCommand for chat_id: {$chatId}", 'telegram');
                return $this->asJson(['ok' => false, 'error' => 'invalid_registration_in_start']);
            }
            return $this->handleStart($chatId, $registration);
        }
        
        return $this->asJson(['ok' => true]);
    }

    /**
     * Начало процесса регистрации
     */
    private function handleStart($chatId, $registration)
    {
        // Проверяем что registration является объектом
        if (!$registration || !is_object($registration)) {
            \Yii::error("Invalid registration object for chat_id: {$chatId}", 'telegram');
            return $this->asJson(['ok' => false, 'error' => 'invalid_registration']);
        }
        
        $registration->status = TemporaryRegistration::STEP_LANGUAGE;
        $registration->save();

        // Логируем начало регистрации
        WorkerLog::logRegistrationStep($chatId, TemporaryRegistration::STEP_START, [
            'registration_id' => $registration->id
        ]);

        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '🇷🇺 Русский', 'callback_data' => 'lang_ru'],
                    ['text' => '🇺🇿 O\'zbekcha', 'callback_data' => 'lang_uz'],
                ],
                [
                    ['text' => '🇬🇧 English', 'callback_data' => 'lang_en'],
                ]
            ]
        ];

        $text = "👋 Vacant ga xush kelibsiz!\n\nTilni tanlang:";
        $this->telegramService->sendMessage($chatId, $text, $keyboard);
        
        return $this->asJson(['ok' => true]);
    }

    /**
     * Обработка callback query
     */
    private function handleCallback($callbackQuery, $registration)
    {
        // Проверяем что registration является объектом
        if (!$registration || !is_object($registration)) {
            $chatId = $callbackQuery['from']['id'];
            \Yii::error("Invalid registration object for chat_id: {$chatId} in handleCallback", 'telegram');
            return $this->asJson(['ok' => false, 'error' => 'invalid_registration']);
        }
        
        $data = $callbackQuery['data'];
        $chatId = $callbackQuery['from']['id'];

        if (strpos($data, 'lang_') === 0) {
            return $this->handleLanguageSelection($chatId, $registration, $data);
        }

        if ($data === 'confirm_registration') {
            return $this->handleConfirmRegistration($chatId, $registration);
        }

        if ($data === 'additional_info') {
            return $this->handleAdditionalInfo($chatId, $registration);
        }

        if ($data === 'profession_selected') {
            return $this->handleProfessionSelected($chatId, $registration);
        }

        if ($data === 'edit_data') {
            return $this->handleEditData($chatId, $registration);
        }

        return $this->asJson(['ok' => true]);
    }

    /**
     * Выбор языка
     */
    private function handleLanguageSelection($chatId, $registration, $langData)
    {
        $language = str_replace('lang_', '', $langData);
        $registration->language = $language;
        $registration->status = TemporaryRegistration::STEP_NAME;
        $registration->save();

        // Логируем выбор языка
        WorkerLog::logRegistrationStep($chatId, TemporaryRegistration::STEP_LANGUAGE, [
            'language' => $language,
            'registration_id' => $registration->id
        ]);

        // Устанавливаем язык для переводов
        \Yii::$app->language = $language;
        
        $text = $this->getMessage('language.selected');
        $this->telegramService->sendMessage($chatId, $text);

        return $this->asJson(['ok' => true]);
    }

    /**
     * Обработка текстового сообщения
     */
    private function handleTextMessage($message, $registration)
    {
        $chatId = $message['chat']['id'];
        
        // Проверяем что registration является объектом
        if (!$registration || !is_object($registration)) {
            \Yii::error("Invalid registration object for chat_id: {$chatId} in handleTextMessage", 'telegram');
            return $this->asJson(['ok' => false, 'error' => 'invalid_registration']);
        }
        
        $text = trim($message['text']);

        switch ($registration->status) {
            case TemporaryRegistration::STEP_LANGUAGE:
                // На этапе выбора языка игнорируем текстовый ввод - только кнопки
                $reminderText = $this->getMessage('language.use_button', [], 'ru'); // Используем русский так как язык еще не выбран
                $this->telegramService->sendMessage($chatId, $reminderText);
                return $this->asJson(['ok' => true]);
            
            case TemporaryRegistration::STEP_NAME:
                return $this->handleNameInput($chatId, $registration, $text);
            
            case TemporaryRegistration::STEP_PHONE:
                // На этапе телефона игнорируем текстовый ввод и напоминаем использовать кнопку
                $reminderText = $this->getMessage('phone.use_button', [], $registration->language);
                $this->telegramService->sendMessage($chatId, $reminderText);
                return $this->asJson(['ok' => true]);
            
            case TemporaryRegistration::STEP_LOCATION:
                // На этапе локации игнорируем текстовый ввод и напоминаем использовать кнопку
                $reminderText = $this->getMessage('location.use_button', [], $registration->language);
                $this->telegramService->sendMessage($chatId, $reminderText);
                return $this->asJson(['ok' => true]);
            
            case TemporaryRegistration::STEP_AGE:
                return $this->handleAgeInput($chatId, $registration, $text);
            
            case TemporaryRegistration::STEP_PROFESSION:
                // На этапе профессий игнорируем текстовый ввод - только WebView
                $reminderText = $this->getMessage('profession.use_button', [], $registration->language);
                $this->telegramService->sendMessage($chatId, $reminderText);
                return $this->asJson(['ok' => true]);
            
            case TemporaryRegistration::STEP_EXPERIENCE:
                return $this->handleExperienceInput($chatId, $registration, $text);
            
            case TemporaryRegistration::STEP_AUDIO:
                // На этапе аудио игнорируем текстовый ввод - только голосовые сообщения
                $reminderText = $this->getMessage('audio.use_voice', [], $registration->language);
                $this->telegramService->sendMessage($chatId, $reminderText);
                return $this->asJson(['ok' => true]);
            
            // Обработка редактирования
            case TemporaryRegistration::STEP_EDIT_NAME:
                return $this->handleEditNameInput($chatId, $registration, $text);
            
            case TemporaryRegistration::STEP_EDIT_AGE:
                return $this->handleEditAgeInput($chatId, $registration, $text);
            
            case TemporaryRegistration::STEP_EDIT_EXPERIENCE:
                return $this->handleEditExperienceInput($chatId, $registration, $text);
            
            case TemporaryRegistration::STEP_EDIT_AUDIO:
                // При редактировании аудио тоже только голосовые сообщения
                $reminderText = $this->getMessage('audio.use_voice', [], $registration->language);
                $this->telegramService->sendMessage($chatId, $reminderText);
                return $this->asJson(['ok' => true]);
        }

        return $this->asJson(['ok' => true]);
    }

    /**
     * Ввод имени
     */
    private function handleNameInput($chatId, $registration, $name)
    {
        // Валидация имени - только буквы, пробелы и дефисы
        if (!$this->validateName($name)) {
            $text = $this->getMessage('name.invalid', [], $registration->language);
            $this->telegramService->sendMessage($chatId, $text);
            return $this->asJson(['ok' => true]);
        }

        $registration->name = $name;
        $registration->status = TemporaryRegistration::STEP_PHONE;
        $registration->save();

        // Создаем Worker сразу при вводе имени
        $worker = $this->createOrUpdateWorker($registration);

        // Логируем ввод имени
        WorkerLog::logRegistrationStep($chatId, TemporaryRegistration::STEP_NAME, [
            'name' => $name,
            'registration_id' => $registration->id,
            'worker_id' => $worker ? $worker->id : null
        ], $worker ? $worker->id : null);

        // Добавляем кнопку "Поделиться номером телефона"
        $sharePhoneButtonText = $this->getMessage('webapp.share_phone', [], $registration->language);
        $keyboard = [
            'keyboard' => [
                [
                    ['text' => $sharePhoneButtonText, 'request_contact' => true]
                ]
            ],
            'resize_keyboard' => true,
            'one_time_keyboard' => true
        ];

        $text = $this->getMessage('name.entered', ['name' => $name], $registration->language);
        $this->telegramService->sendMessage($chatId, $text, $keyboard);

        return $this->asJson(['ok' => true]);
    }

    /**
     * Обработка контакта (номер телефона)
     */
    private function handleContact($message, $registration)
    {
        $chatId = $message['chat']['id'];
        
        // Проверяем что registration является объектом
        if (!$registration || !is_object($registration)) {
            \Yii::error("Invalid registration object for chat_id: {$chatId} in handleContact", 'telegram');
            return $this->asJson(['ok' => false, 'error' => 'invalid_registration']);
        }
        
        $contact = $message['contact'];
        $phone = $contact['phone_number'];
        
        // Проверяем, что пользователь отправил свой собственный номер
        if ($contact['user_id'] != $chatId) {
            $text = $this->getMessage('phone.not_your_own', [], $registration->language);
            $this->telegramService->sendMessage($chatId, $text);
            return $this->asJson(['ok' => true]);
        }

        $registration->phone = $phone;
        
        // Проверяем, это редактирование или обычная регистрация
        if ($registration->status == TemporaryRegistration::STEP_EDIT_PHONE) {
            // Это редактирование - обновляем Worker и возвращаем в меню
            return $this->handleEditPhoneComplete($chatId, $registration, $phone);
        } else {
            // Обычная регистрация
            $registration->status = TemporaryRegistration::STEP_LOCATION;
            $registration->save();
        }

        // Обновляем Worker
        $worker = $this->createOrUpdateWorker($registration);

        // Логируем ввод телефона через контакт
        WorkerLog::logRegistrationStep($chatId, TemporaryRegistration::STEP_PHONE, [
            'phone' => $phone,
            'method' => 'contact_share',
            'registration_id' => $registration->id,
            'worker_id' => $worker ? $worker->id : null
        ], $worker ? $worker->id : null);

        $locationButtonText = $this->getMessage('webapp.send_location', [], $registration->language);
        $keyboard = [
            'keyboard' => [
                [
                    ['text' => $locationButtonText, 'request_location' => true]
                ]
            ],
            'resize_keyboard' => true,
            'one_time_keyboard' => true
        ];

        $text = $this->getMessage('phone.entered', ['phone' => $phone], $registration->language);
        $this->telegramService->sendMessage($chatId, $text, $keyboard);

        return $this->asJson(['ok' => true]);
    }

    /**
     * Обратное геокодирование - получение адреса по координатам
     */
    private function reverseGeocode($latitude, $longitude, $language = 'ru')
    {
        try {
            // Пробуем Nominatim OpenStreetMap (бесплатный)
            $nominatimUrl = "https://nominatim.openstreetmap.org/reverse";
            $params = [
                'format' => 'json',
                'lat' => $latitude,
                'lon' => $longitude,
                'accept-language' => $language == 'uz' ? 'uz,ru,en' : ($language . ',en'),
                'addressdetails' => 1,
                'zoom' => 18
            ];
            
            $url = $nominatimUrl . '?' . http_build_query($params);
            
            $context = stream_context_create([
                'http' => [
                    'timeout' => 10,
                    'method' => 'GET',
                    'header' => [
                        "User-Agent: IshTop-Bot/1.0 (https://ish-top.uz)",
                        "Accept: application/json"
                    ]
                ]
            ]);
            
            $response = file_get_contents($url, false, $context);
            
            if ($response !== false) {
                $data = json_decode($response, true);
                
                if ($data && isset($data['display_name'])) {
                    return $this->formatAddress($data, $language);
                }
            }
            
            \Yii::warning("Nominatim geocoding failed for coordinates: {$latitude}, {$longitude}", 'telegram');
            
            // Fallback: пробуем Yandex Maps Geocoding API (хорошо работает для СНГ)
            return $this->reverseGeocodeYandex($latitude, $longitude, $language);
            
        } catch (\Exception $e) {
            \Yii::error("Reverse geocoding error: " . $e->getMessage(), 'telegram');
            return null;
        }
    }
    
    /**
     * Обратное геокодирование через Yandex Maps
     */
    private function reverseGeocodeYandex($latitude, $longitude, $language = 'ru')
    {
        try {
            $yandexUrl = "https://geocode-maps.yandex.ru/1.x/";
            $params = [
                'geocode' => $longitude . ',' . $latitude, // Yandex требует lon,lat
                'format' => 'json',
                'lang' => $language == 'uz' ? 'ru_RU' : $language . '_RU',
                'kind' => 'house',
                'results' => 1
            ];
            
            $url = $yandexUrl . '?' . http_build_query($params);
            
            $context = stream_context_create([
                'http' => [
                    'timeout' => 10,
                    'method' => 'GET',
                    'header' => [
                        "User-Agent: IshTop-Bot/1.0 (https://ish-top.uz)",
                        "Accept: application/json"
                    ]
                ]
            ]);
            
            $response = file_get_contents($url, false, $context);
            
            if ($response !== false) {
                $data = json_decode($response, true);
                
                if ($data && isset($data['response']['GeoObjectCollection']['featureMember'][0])) {
                    $geoObject = $data['response']['GeoObjectCollection']['featureMember'][0]['GeoObject'];
                    return $this->formatYandexAddress($geoObject, $language);
                }
            }
            
            \Yii::warning("Yandex geocoding failed for coordinates: {$latitude}, {$longitude}", 'telegram');
            return null;
            
        } catch (\Exception $e) {
            \Yii::error("Yandex reverse geocoding error: " . $e->getMessage(), 'telegram');
            return null;
        }
    }
    
    /**
     * Форматирование адреса из Nominatim
     */
    private function formatAddress($data, $language)
    {
        $address = $data['address'] ?? [];
        $parts = [];
        
        // Город или населенный пункт
        if (isset($address['city'])) {
            $parts[] = $address['city'];
        } elseif (isset($address['town'])) {
            $parts[] = $address['town'];
        } elseif (isset($address['village'])) {
            $parts[] = $address['village'];
        } elseif (isset($address['suburb'])) {
            $parts[] = $address['suburb'];
        }
        
        // Район
        if (isset($address['suburb']) && !in_array($address['suburb'], $parts)) {
            $parts[] = $address['suburb'];
        } elseif (isset($address['city_district'])) {
            $parts[] = $address['city_district'];
        }
        
        // Улица и номер дома
        $streetParts = [];
        if (isset($address['house_number'])) {
            $streetParts[] = $address['house_number'];
        }
        if (isset($address['road'])) {
            $streetParts[] = $address['road'];
        }
        
        if (!empty($streetParts)) {
            $parts[] = implode(', ', $streetParts);
        }
        
        // Регион/область
        if (isset($address['state'])) {
            $parts[] = $address['state'];
        }
        
        return implode(', ', array_filter($parts)) ?: $data['display_name'] ?? null;
    }
    
    /**
     * Форматирование адреса из Yandex
     */
    private function formatYandexAddress($geoObject, $language)
    {
        $metaData = $geoObject['metaDataProperty']['GeocoderMetaData'] ?? [];
        $addressDetails = $metaData['Address']['Components'] ?? [];
        
        $parts = [];
        $street = '';
        $house = '';
        
        foreach ($addressDetails as $component) {
            $kind = $component['kind'];
            $name = $component['name'];
            
            switch ($kind) {
                case 'country':
                    // Пропускаем страну
                    break;
                case 'province':
                case 'area':
                    $parts[] = $name;
                    break;
                case 'locality':
                    $parts[] = $name;
                    break;
                case 'district':
                    if (!in_array($name, $parts)) {
                        $parts[] = $name;
                    }
                    break;
                case 'street':
                    $street = $name;
                    break;
                case 'house':
                    $house = $name;
                    break;
            }
        }
        
        // Добавляем улицу и дом
        if ($street) {
            $streetWithHouse = $street;
            if ($house) {
                $streetWithHouse .= ', ' . $house;
            }
            $parts[] = $streetWithHouse;
        }
        
        return implode(', ', array_filter($parts)) ?: ($geoObject['name'] ?? null);
    }

    /**
     * Обработка геолокации
     */
    private function handleLocation($message, $registration)
    {
        $chatId = $message['chat']['id'];
        
        // Проверяем что registration является объектом
        if (!$registration || !is_object($registration)) {
            \Yii::error("Invalid registration object for chat_id: {$chatId} in handleLocation", 'telegram');
            return $this->asJson(['ok' => false, 'error' => 'invalid_registration']);
        }
        
        $location = $message['location'];
        $latitude = $location['latitude'];
        $longitude = $location['longitude'];

        $registration->lat = $latitude;
        $registration->long = $longitude;
        
        // Проверяем, это редактирование или обычная регистрация
        if ($registration->status == TemporaryRegistration::STEP_EDIT_LOCATION) {
            // Это редактирование - обновляем Worker и возвращаем в меню
            return $this->handleEditLocationComplete($chatId, $registration, $latitude, $longitude);
        } else {
            // Обычная регистрация
            $registration->status = TemporaryRegistration::STEP_AGE;
            $registration->save();
        }

        // Логируем отправку геолокации
        WorkerLog::logRegistrationStep($chatId, TemporaryRegistration::STEP_LOCATION, [
            'latitude' => $latitude,
            'longitude' => $longitude,
            'registration_id' => $registration->id
        ]);

        // Получаем адрес по координатам
        $address = $this->reverseGeocode($latitude, $longitude, $registration->language);

        // Убираем клавиатуру
        $removeKeyboard = ['remove_keyboard' => true];

        // Формируем сообщение с адресом
        if ($address) {
            $text = $this->getMessage('location.received_with_address', [
                'address' => $address
            ], $registration->language);
        } else {
            $text = $this->getMessage('location.received', [], $registration->language);
        }
        
        $this->telegramService->sendMessage($chatId, $text, $removeKeyboard);

        return $this->asJson(['ok' => true]);
    }

    /**
     * Ввод возраста
     */
    private function handleAgeInput($chatId, $registration, $ageText)
    {
        // Проверяем что введены только цифры
        if (!$this->validateNumeric($ageText)) {
            $text = $this->getMessage('age.numbers_only', [], $registration->language);
            $this->telegramService->sendMessage($chatId, $text);
            return $this->asJson(['ok' => true]);
        }

        $age = intval($ageText);
        
        if ($age < 16 || $age > 80) {
            $text = $this->getMessage('age.invalid', [], $registration->language);
            $this->telegramService->sendMessage($chatId, $text);
            return $this->asJson(['ok' => true]);
        }

        $registration->age = $age;
        $registration->status = TemporaryRegistration::STEP_PROFESSION;
        $registration->save();

        // Логируем ввод возраста
        WorkerLog::logRegistrationStep($chatId, TemporaryRegistration::STEP_AGE, [
            'age' => $age,
            'registration_id' => $registration->id
        ]);

        // Открываем WebView для выбора профессии с передачей языка
        $baseUrl = \Yii::$app->params['webUrl'] ?? \Yii::$app->request->hostInfo;
        $webViewUrl = $baseUrl . '/telegram/profession/select?chat_id=' . $chatId . '&language=' . $registration->language;
        
        $activityButtonText = $this->getMessage('webapp.select_activity', [], $registration->language);
        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => $activityButtonText, 'web_app' => ['url' => $webViewUrl]]
                ]
            ]
        ];

        $text = $this->getMessage('age.entered', ['age' => $age], $registration->language);
        $this->telegramService->sendMessage($chatId, $text, $keyboard);

        return $this->asJson(['ok' => true]);
    }

    /**
     * Ввод опыта работы
     */
    private function handleExperienceInput($chatId, $registration, $experienceText)
    {
        // Проверяем что введены только цифры
        if (!$this->validateNumeric($experienceText)) {
            $text = $this->getMessage('experience.numbers_only', [], $registration->language);
            $this->telegramService->sendMessage($chatId, $text);
            return $this->asJson(['ok' => true]);
        }

        $experience = intval($experienceText);
        
        if ($experience < 0 || $experience > 50) {
            $text = $this->getMessage('experience.invalid', [], $registration->language);
            $this->telegramService->sendMessage($chatId, $text);
            return $this->asJson(['ok' => true]);
        }

        $registration->experience_years = $experience;
        $registration->status = TemporaryRegistration::STEP_AUDIO;
        $registration->save();

        // Обновляем Worker
        $worker = $this->createOrUpdateWorker($registration);

        // Логируем ввод опыта
        WorkerLog::logRegistrationStep($chatId, TemporaryRegistration::STEP_EXPERIENCE, [
            'experience_years' => $experience,
            'registration_id' => $registration->id,
            'worker_id' => $worker ? $worker->id : null
        ], $worker ? $worker->id : null);

        $yearsText = $this->getYearsText($experience, $registration->language);
        $text = $this->getMessage('experience.entered', [
            'experience' => $experience, 
            'years_text' => $yearsText
        ], $registration->language);
        $this->telegramService->sendMessage($chatId, $text);

        return $this->asJson(['ok' => true]);
    }

    /**
     * Обработка аудиозаписи
     */
    private function handleVoice($message, $registration)
    {
        $chatId = $message['chat']['id'];
        
        // Проверяем что registration является объектом
        if (!$registration || !is_object($registration)) {
            \Yii::error("Invalid registration object for chat_id: {$chatId} in handleVoice", 'telegram');
            return $this->asJson(['ok' => false, 'error' => 'invalid_registration']);
        }
        
        $voice = $message['voice'];

        // Скачиваем аудиофайл
        $audioUrl = $this->downloadVoiceFile($voice['file_id']);
        
        if ($audioUrl) {
            $registration->audio_file_url = $audioUrl;
        }
        
        // Проверяем, это редактирование или обычная регистрация
        if ($registration->status == TemporaryRegistration::STEP_EDIT_AUDIO) {
            // Это редактирование - обновляем Worker и возвращаем в меню
            return $this->handleEditAudioComplete($chatId, $registration, $audioUrl);
        } else {
            // Обычная регистрация
            $registration->status = TemporaryRegistration::STEP_CONFIRMATION;
            $registration->save();
        }

        // Логируем загрузку аудио
        WorkerLog::logRegistrationStep($chatId, TemporaryRegistration::STEP_AUDIO, [
            'audio_file_url' => $audioUrl ?: 'failed',
            'file_id' => $voice['file_id'],
            'duration' => $voice['duration'] ?? null,
            'registration_id' => $registration->id
        ]);

        // Получаем все данные для подтверждения
        $this->sendConfirmationMessage($chatId, $registration, $audioUrl ? true : false);

        return $this->asJson(['ok' => true]);
    }

    /**
     * Отправка сообщения с подтверждением всех данных
     */
    private function sendConfirmationMessage($chatId, $registration, $audioUploaded = false)
    {
        // Получаем профессии пользователя
        $worker = \app\modules\worker\models\Worker::findOne(['chat_id' => $chatId]);
        $professions = [];
        
        if ($worker) {
            $professionRecords = \Yii::$app->db->createCommand(
                'SELECT p.* FROM professions p 
                 JOIN worker_professions wp ON p.id = wp.profession_id 
                 WHERE wp.worker_id = :worker_id AND p.deleted_at IS NULL'
            )->bindValue(':worker_id', $worker->id)->queryAll();
            
            foreach ($professionRecords as $record) {
                $profession = new Profession();
                $profession->setAttributes($record);
                $professions[] = $profession->getName($registration->language);
            }
        }
        
        $professionList = !empty($professions) ? implode(', ', $professions) : '-';
        
        // Получаем правильное склонение лет
        $yearsText = $this->getYearsText($registration->experience_years, $registration->language);
        
        // Статус аудио
        $audioStatus = $audioUploaded ? 
            $this->getMessage('audio.uploaded', [], $registration->language) : 
            $this->getMessage('audio.not_uploaded', [], $registration->language);
        
        // Получаем адрес по координатам для подтверждения
        $locationText = $this->getMessage('location.received_short', [], $registration->language);
        if ($registration->lat && $registration->long) {
            $address = $this->reverseGeocode($registration->lat, $registration->long, $registration->language);
            if ($address) {
                $locationText = $address;
            }
        }
        
        // Формируем сообщение с подтверждением данных
        $text = $this->getMessage('confirm.data', [
            'name' => $registration->name,
            'phone' => $registration->phone,
            'age' => $registration->age,
            'location' => $locationText,
            'professions' => $professionList,
            'experience' => $registration->experience_years,
            'years_text' => $yearsText,
            'audio_status' => $audioStatus
        ], $registration->language);
        
        // Только кнопка подтверждения 
        $confirmButtonText = $this->getMessage('button.confirm_registration', [], $registration->language);
        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => $confirmButtonText, 'callback_data' => 'confirm_registration']
                ]
            ]
        ];

        $this->telegramService->sendMessage($chatId, $text, $keyboard);
    }

    /**
     * Подтверждение регистрации
     */
    private function handleConfirmRegistration($chatId, $registration)
    {
        // Обновляем Worker и завершаем регистрацию
        $worker = $this->createOrUpdateWorker($registration);
        
        if ($worker) {
            $worker->status = 1; // Активный статус
            $worker->save();
        }

        $registration->status = TemporaryRegistration::STEP_COMPLETED;
        $registration->save();

        // Логируем завершение регистрации
        WorkerLog::logRegistrationStep($chatId, TemporaryRegistration::STEP_COMPLETED, [
            'worker_id' => $worker ? $worker->id : null,
            'registration_id' => $registration->id
        ], $worker ? $worker->id : null);

        // Отправляем сообщение о завершении с кнопками управления
        $downloadLinks = $this->getDownloadLinks();
        $text = $this->getMessage('registration.completed', ['downloadLinks' => $downloadLinks], $registration->language);
        
        // Добавляем кнопки редактирования и дополнительной информации
        $editButtonText = $this->getMessage('button.edit_data', [], $registration->language);
        $additionalInfoButtonText = $this->getMessage('button.additional_info', [], $registration->language);
        
        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => $editButtonText, 'callback_data' => 'edit_data'],
                    ['text' => $additionalInfoButtonText, 'callback_data' => 'additional_info']
                ]
            ]
        ];

        $this->telegramService->sendMessage($chatId, $text, $keyboard);

        return $this->asJson(['ok' => true]);
    }

    /**
     * Создание или обновление работника на каждом шаге
     */
    private function createOrUpdateWorker($registration)
    {
        // Ищем существующего работника по chat_id
        $worker = \app\modules\worker\models\Worker::findOne(['chat_id' => $registration->chat_id]);
        
        if (!$worker) {
            $worker = new \app\modules\worker\models\Worker();
            $worker->chat_id = $registration->chat_id;
            $worker->status = 0; // В процессе регистрации
        }
        
        // Обновляем данные из регистрации
        if ($registration->name) $worker->name = $registration->name;
        if ($registration->phone) $worker->phone = $registration->phone;
        if ($registration->age) $worker->age = $registration->age;
        if ($registration->lat) $worker->lat = $registration->lat;
        if ($registration->long) $worker->long = $registration->long;
        if ($registration->language) $worker->language = $registration->language;
        if ($registration->experience_years) $worker->experience_years = $registration->experience_years;
        if ($registration->audio_file_url) $worker->audio_file_url = $registration->audio_file_url;
        if ($registration->about) $worker->about = $registration->about;
        
        return $worker->save() ? $worker : null;
    }

    /**
     * Скачивание аудиофайла
     */
    private function downloadVoiceFile($fileId)
    {
        try {
            \Yii::info("Starting audio download for file_id: {$fileId}", 'telegram');
            
            // Получаем информацию о файле
            $fileInfo = $this->telegramService->makeRequest('getFile', ['file_id' => $fileId]);
            
            if (!$fileInfo['ok']) {
                \Yii::error("Failed to get file info for {$fileId}: " . json_encode($fileInfo), 'telegram');
                return false;
            }

            $filePath = $fileInfo['result']['file_path'];
            $fileUrl = "https://api.telegram.org/file/bot{$this->telegramService->botToken}/{$filePath}";
            
            \Yii::info("File URL: {$fileUrl}", 'telegram');

            // Создаем папку для аудио
            $audioDir = \Yii::getAlias('@webroot/uploads/audio');
            if (!is_dir($audioDir)) {
                if (!mkdir($audioDir, 0755, true)) {
                    \Yii::error("Failed to create audio directory: {$audioDir}", 'telegram');
                    return false;
                }
            }

            // Генерируем имя файла
            $fileName = time() . '_' . uniqid() . '.ogg';
            $localPath = $audioDir . DIRECTORY_SEPARATOR . $fileName;
            
            \Yii::info("Local path: {$localPath}", 'telegram');

            // Скачиваем файл с улучшенными настройками
            $context = stream_context_create([
                'http' => [
                    'timeout' => 60,
                    'method' => 'GET',
                    'header' => "User-Agent: Mozilla/5.0 (compatible; Bot)\r\n"
                ]
            ]);

            $fileContent = file_get_contents($fileUrl, false, $context);
            
            if ($fileContent !== false) {
                if (file_put_contents($localPath, $fileContent)) {
                    \Yii::info("Audio file downloaded successfully: {$fileName} (" . strlen($fileContent) . " bytes)", 'telegram');
                    return '/uploads/audio/' . $fileName;
                } else {
                    \Yii::error("Failed to write audio file to {$localPath}", 'telegram');
                    return false;
                }
            } else {
                \Yii::error("Failed to download audio content from {$fileUrl}", 'telegram');
                return false;
            }
            
        } catch (\Exception $e) {
            \Yii::error("Exception in downloadVoiceFile: " . $e->getMessage() . "\nStack trace: " . $e->getTraceAsString(), 'telegram');
            return false;
        }
    }

    /**
     * Получение ссылок на скачивание приложения
     */
    private function getDownloadLinks()
    {
        return "🤖 Android: https://play.google.com/store/apps/details?id=com.ishtop.app\n" .
               "🍎 iOS: https://apps.apple.com/app/ishtop/id123456789\n";
    }

    /**
     * Получение chat_id из update
     */
    private function getChatId($update)
    {
        if (isset($update['message']['chat']['id'])) {
            return $update['message']['chat']['id'];
        }
        
        if (isset($update['callback_query']['from']['id'])) {
            return $update['callback_query']['from']['id'];
        }

        return null;
    }

    /**
     * Обработка дополнительной информации
     */
    private function handleAdditionalInfo($chatId, $registration)
    {
        $registration->status = TemporaryRegistration::STEP_ADDITIONAL_INFO;
        $registration->save();

        $text = $this->getMessage('additional.info', [], $registration->language);
        $this->sendMessage($chatId, $text);

        return $this->asJson(['ok' => true]);
    }

    /**
     * Обработка кнопки редактирования данных
     */
    private function handleEditData($chatId, $registration)
    {
        // Перенаправляем в EditController
        $editController = new \app\modules\telegram\controllers\EditController('edit', \Yii::$app->getModule('telegram'));
        $editController->enableCsrfValidation = false;
        
        // Передаем данные о текущем пользователе
        return $editController->actionShowEditMenu($chatId, $registration->language);
    }

    /**
     * Отправка сообщения (упрощенная версия)
     */
    private function sendMessage($chatId, $text, $keyboard = null)
    {
        if ($this->telegramService) {
            return $this->telegramService->sendMessage($chatId, $text, $keyboard);
        }

        // Fallback для логирования
        \Yii::info("Send message to {$chatId}: {$text}", 'telegram');
        return ['ok' => true];
    }

    // ==================== МЕТОДЫ РЕДАКТИРОВАНИЯ ====================

    /**
     * Обработка ввода нового имени
     */
    private function handleEditNameInput($chatId, $registration, $name)
    {
        // Валидация имени - только буквы, пробелы и дефисы
        if (!$this->validateName($name)) {
            $text = $this->getMessage('name.invalid', [], $registration->language);
            $this->telegramService->sendMessage($chatId, $text);
            return $this->asJson(['ok' => true]);
        }

        // Обновляем Worker
        $worker = \app\modules\worker\models\Worker::findOne(['chat_id' => $chatId]);
        if ($worker) {
            $worker->name = $name;
            $worker->save();
        }

        // Сбрасываем состояние редактирования
        $registration->status = TemporaryRegistration::STEP_COMPLETED;
        $registration->save();

        // Показываем успешное обновление и возвращаем в меню редактирования
        $text = $this->getMessage('edit.name.success', ['new_name' => $name], $registration->language);
        $this->telegramService->sendMessage($chatId, $text);

        // Возвращаем в меню редактирования
        return $this->returnToEditMenu($chatId, $registration->language);
    }

    /**
     * Обработка ввода нового возраста
     */
    private function handleEditAgeInput($chatId, $registration, $ageText)
    {
        // Проверяем что введены только цифры
        if (!$this->validateNumeric($ageText)) {
            $text = $this->getMessage('age.numbers_only', [], $registration->language);
            $this->telegramService->sendMessage($chatId, $text);
            return $this->asJson(['ok' => true]);
        }

        $age = intval($ageText);
        
        if ($age < 16 || $age > 80) {
            $text = $this->getMessage('age.invalid', [], $registration->language);
            $this->telegramService->sendMessage($chatId, $text);
            return $this->asJson(['ok' => true]);
        }

        // Обновляем Worker
        $worker = \app\modules\worker\models\Worker::findOne(['chat_id' => $chatId]);
        if ($worker) {
            $worker->age = $age;
            $worker->save();
        }

        // Сбрасываем состояние редактирования
        $registration->status = TemporaryRegistration::STEP_COMPLETED;
        $registration->save();

        // Показываем успешное обновление
        $text = $this->getMessage('edit.age.success', ['new_age' => $age], $registration->language);
        $this->telegramService->sendMessage($chatId, $text);

        // Возвращаем в меню редактирования
        return $this->returnToEditMenu($chatId, $registration->language);
    }

    /**
     * Обработка ввода нового опыта работы
     */
    private function handleEditExperienceInput($chatId, $registration, $experienceText)
    {
        // Проверяем что введены только цифры
        if (!$this->validateNumeric($experienceText)) {
            $text = $this->getMessage('experience.numbers_only', [], $registration->language);
            $this->telegramService->sendMessage($chatId, $text);
            return $this->asJson(['ok' => true]);
        }

        $experience = intval($experienceText);
        
        if ($experience < 0 || $experience > 50) {
            $text = $this->getMessage('experience.invalid', [], $registration->language);
            $this->telegramService->sendMessage($chatId, $text);
            return $this->asJson(['ok' => true]);
        }

        // Обновляем Worker
        $worker = \app\modules\worker\models\Worker::findOne(['chat_id' => $chatId]);
        if ($worker) {
            $worker->experience_years = $experience;
            $worker->save();
        }

        // Сбрасываем состояние редактирования
        $registration->status = TemporaryRegistration::STEP_COMPLETED;
        $registration->save();

        // Показываем успешное обновление
        $yearsText = $this->getYearsText($experience, $registration->language);
        $text = $this->getMessage('edit.experience.success', [
            'new_experience' => $experience,
            'years_text' => $yearsText
        ], $registration->language);
        $this->telegramService->sendMessage($chatId, $text);

        // Возвращаем в меню редактирования
        return $this->returnToEditMenu($chatId, $registration->language);
    }

    /**
     * Завершение редактирования телефона
     */
    private function handleEditPhoneComplete($chatId, $registration, $phone)
    {
        // Обновляем Worker
        $worker = \app\modules\worker\models\Worker::findOne(['chat_id' => $chatId]);
        if ($worker) {
            $worker->phone = $phone;
            $worker->save();
        }

        // Сбрасываем состояние редактирования
        $registration->status = TemporaryRegistration::STEP_COMPLETED;
        $registration->save();

        // Убираем клавиатуру и показываем успешное обновление
        $removeKeyboard = ['remove_keyboard' => true];
        $text = $this->getMessage('edit.phone.success', ['new_phone' => $phone], $registration->language);
        $this->telegramService->sendMessage($chatId, $text, $removeKeyboard);

        // Возвращаем в меню редактирования
        return $this->returnToEditMenu($chatId, $registration->language);
    }

    /**
     * Завершение редактирования локации
     */
    private function handleEditLocationComplete($chatId, $registration, $latitude, $longitude)
    {
        // Обновляем Worker
        $worker = \app\modules\worker\models\Worker::findOne(['chat_id' => $chatId]);
        if ($worker) {
            $worker->lat = $latitude;
            $worker->long = $longitude;
            $worker->save();
        }

        // Сбрасываем состояние редактирования
        $registration->status = TemporaryRegistration::STEP_COMPLETED;
        $registration->save();

        // Получаем адрес по координатам
        $address = $this->reverseGeocode($latitude, $longitude, $registration->language);

        // Убираем клавиатуру и показываем успешное обновление
        $removeKeyboard = ['remove_keyboard' => true];
        if ($address) {
            $text = $this->getMessage('edit.location.success_with_address', ['address' => $address], $registration->language);
        } else {
            $text = $this->getMessage('edit.location.success', [], $registration->language);
        }
        $this->telegramService->sendMessage($chatId, $text, $removeKeyboard);

        // Возвращаем в меню редактирования
        return $this->returnToEditMenu($chatId, $registration->language);
    }

    /**
     * Завершение редактирования аудио
     */
    private function handleEditAudioComplete($chatId, $registration, $audioUrl)
    {
        // Обновляем Worker и удаляем старый аудиофайл
        $worker = \app\modules\worker\models\Worker::findOne(['chat_id' => $chatId]);
        if ($worker) {
            // Удаляем старый аудиофайл
            if ($worker->audio_file_url) {
                $this->deleteOldAudioFile($worker->audio_file_url);
            }
            
            // Устанавливаем новый
            if ($audioUrl) {
                $worker->audio_file_url = $audioUrl;
            }
            $worker->save();
        }

        // Сбрасываем состояние редактирования
        $registration->status = TemporaryRegistration::STEP_COMPLETED;
        $registration->save();

        // Показываем успешное обновление
        if ($audioUrl) {
            $text = $this->getMessage('edit.audio.success', [], $registration->language);
        } else {
            $text = $this->getMessage('edit.audio.failed', [], $registration->language);
        }
        $this->telegramService->sendMessage($chatId, $text);

        // Возвращаем в меню редактирования
        return $this->returnToEditMenu($chatId, $registration->language);
    }

    /**
     * Удаление старого аудиофайла
     */
    private function deleteOldAudioFile($audioUrl)
    {
        if (!$audioUrl) {
            return;
        }

        try {
            // Получаем полный путь к файлу
            $filePath = \Yii::getAlias('@webroot') . $audioUrl;
            
            if (file_exists($filePath)) {
                if (unlink($filePath)) {
                    \Yii::info("Old audio file deleted: {$filePath}", 'telegram');
                } else {
                    \Yii::warning("Failed to delete old audio file: {$filePath}", 'telegram');
                }
            }
        } catch (\Exception $e) {
            \Yii::error("Error deleting old audio file: " . $e->getMessage(), 'telegram');
        }
    }

    /**
     * Возврат в меню редактирования
     */
    private function returnToEditMenu($chatId, $language)
    {
        // Создаем EditController и показываем меню
        $editController = new \app\modules\telegram\controllers\EditController('edit', \Yii::$app->getModule('telegram'));
        $editController->enableCsrfValidation = false;
        
        return $editController->actionShowEditMenu($chatId, $language);
    }

    /**
     * Валидация имени - только буквы, пробелы и дефисы
     */
    private function validateName($name)
    {
        // Проверяем что имя содержит только буквы (любых алфавитов), пробелы, дефисы и апострофы
        // Убираем лишние пробелы и проверяем длину
        $name = trim($name);
        if (strlen($name) < 2 || strlen($name) > 50) {
            return false;
        }
        
        // Разрешаем буквы любых алфавитов, пробелы, дефисы и апострофы
        return preg_match('/^[\p{L}\s\-\']+$/u', $name);
    }

    /**
     * Валидация числового ввода - только цифры
     */
    private function validateNumeric($input)
    {
        // Проверяем что строка содержит только цифры
        return preg_match('/^\d+$/', trim($input));
    }

    /**
     * Обработка неподходящих аудиофайлов (музыка, документы)
     */
    private function handleInvalidAudio($message, $registration)
    {
        $chatId = $message['chat']['id'];
        
        // Проверяем что registration является объектом
        if (!$registration || !is_object($registration)) {
            \Yii::error("Invalid registration object for chat_id: {$chatId} in handleInvalidAudio", 'telegram');
            return $this->asJson(['ok' => false, 'error' => 'invalid_registration']);
        }

        // Проверяем, нужно ли аудио на текущем этапе
        if ($registration->status == TemporaryRegistration::STEP_AUDIO || 
            $registration->status == TemporaryRegistration::STEP_EDIT_AUDIO) {
            
            // Отправляем сообщение о том, что нужно голосовое сообщение
            $text = $this->getMessage('audio.voice_only', [], $registration->language);
            $this->telegramService->sendMessage($chatId, $text);
        }

        return $this->asJson(['ok' => true]);
    }

    /**
     * Обработка неподходящих медиафайлов (фото, видео, стикеры и другие)
     */
    private function handleInvalidMedia($message, $registration)
    {
        $chatId = $message['chat']['id'];
        
        // Проверяем что registration является объектом
        if (!$registration || !is_object($registration)) {
            \Yii::error("Invalid registration object for chat_id: {$chatId} in handleInvalidMedia", 'telegram');
            return $this->asJson(['ok' => false, 'error' => 'invalid_registration']);
        }

        // Проверяем, нужно ли медиа на текущем этапе
        if ($registration->status == TemporaryRegistration::STEP_AUDIO || 
            $registration->status == TemporaryRegistration::STEP_EDIT_AUDIO) {
            
            // Отправляем сообщение о том, что нужно голосовое сообщение
            $text = $this->getMessage('media.voice_only', [], $registration->language);
            $this->telegramService->sendMessage($chatId, $text);
        }

        return $this->asJson(['ok' => true]);
    }
} 