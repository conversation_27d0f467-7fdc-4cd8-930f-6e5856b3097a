# Employer API Documentation

Документация API для модуля работодателей (Employer Module).

## Базовый URL
```
{domain}/employer/
```

## Аутентификация

Все методы (кроме публичного просмотра списка работников) требуют авторизации через заголовок:
```
Authorization: Bearer {auth_token}
```

### Методы аутентификации

#### 1. Отправка SMS кода
```
POST /employer/auth/send-code
Content-Type: application/json

{
    "phone": "+998901234567"
}
```

#### 2. Вход по коду
```
POST /employer/auth/login
Content-Type: application/json

{
    "phone": "+998901234567",
    "code": "123456"
}
```

#### 3. Выход
```
POST /employer/auth/logout
Authorization: Bearer {token}
```

#### 4. Проверка токена
```
GET /employer/auth/verify
Authorization: Bearer {token}
```

## Просмотр работников

### 1. Публичный список работников (без авторизации)
```
GET /employer/worker/list?page=1&limit=20&profession_id=1&experience_from=2&experience_to=5&age_from=20&age_to=30&latitude=41.2995&longitude=69.2401&radius=10
```

### 2. Поиск работников (без авторизации)
```
GET /employer/worker/search?query=javascript&page=1&limit=20
```

### 3. Детальная информация о работнике (требует авторизации)
```
GET /employer/worker/detail?worker_id=123
Authorization: Bearer {token}
```

### 4. Список профессий
```
GET /employer/worker/professions
```

## Избранные работники

### 1. Добавить в избранное
```
POST /employer/favorite/add
Authorization: Bearer {token}
Content-Type: application/json

{
    "worker_id": 123
}
```

### 2. Удалить из избранного
```
POST /employer/favorite/remove
Authorization: Bearer {token}
Content-Type: application/json

{
    "worker_id": 123
}
```

### 3. Список избранных
```
GET /employer/favorite/list?page=1&limit=20
Authorization: Bearer {token}
```

### 4. Переключить статус избранного
```
POST /employer/favorite/toggle
Authorization: Bearer {token}
Content-Type: application/json

{
    "worker_id": 123
}
```

### 5. Статистика избранных
```
GET /employer/favorite/statistics
Authorization: Bearer {token}
```

## Профиль работодателя

### 1. Просмотр профиля
```
GET /employer/profile/view
Authorization: Bearer {token}
```

### 2. Обновление профиля
```
PUT /employer/profile/update
Authorization: Bearer {token}
Content-Type: application/json

{
    "name": "John Doe",
    "business_name": "My Company LLC",
    "business_inn": "*********",
    "business_address": "Tashkent, Uzbekistan"
}
```

### 3. Смена языка
```
POST /employer/profile/change-language
Authorization: Bearer {token}
Content-Type: application/json

{
    "language": "ru"
}
```

### 4. Список доступных языков
```
GET /employer/profile/languages
```

### 5. Удаление аккаунта
```
POST /employer/profile/delete
Authorization: Bearer {token}
Content-Type: application/json

{
    "confirm_deletion": "DELETE"
}
```

## Управление вакансиями

### 1. Список вакансий
```
GET /employer/vacancy/list?page=1&limit=20&status=active
Authorization: Bearer {token}
```

### 2. Просмотр вакансии
```
GET /employer/vacancy/view?id=123
Authorization: Bearer {token}
```

### 3. Создание вакансии
```
POST /employer/vacancy/create
Authorization: Bearer {token}
Content-Type: application/json

{
    "title": "Senior Developer",
    "description": "Job description here",
    "profession_id": 1,
    "salary_from": 1000,
    "salary_to": 2000,
    "experience_from": 3,
    "experience_to": 5,
    "status": "active",
    "location": "Tashkent",
    "latitude": 41.2995,
    "longitude": 69.2401
}
```

### 4. Обновление вакансии
```
PUT /employer/vacancy/update
Authorization: Bearer {token}
Content-Type: application/json

{
    "id": 123,
    "title": "Updated title",
    "status": "paused"
}
```

### 5. Удаление вакансии
```
DELETE /employer/vacancy/delete
Authorization: Bearer {token}
Content-Type: application/json

{
    "id": 123
}
```

### 6. Доступные статусы вакансий
```
GET /employer/vacancy/statuses
```

## Коды ответов

- `200` - Успешно
- `400` - Ошибка валидации данных
- `401` - Не авторизован
- `403` - Доступ запрещен
- `404` - Не найдено
- `500` - Внутренняя ошибка сервера

## Формат ответов

### Успешный ответ
```json
{
    "success": true,
    "message": "Operation completed successfully",
    "data": {
        // ответ с данными
    }
}
```

### Ошибка
```json
{
    "success": false,
    "message": "Error message",
    "errors": {
        "field_name": ["Error description"]
    }
}
```

## Поддерживаемые языки

- `uz` - Узбекский (по умолчанию)
- `ru` - Русский  
- `en` - Английский

Язык ответов API зависит от языка, установленного в профиле работодателя.
