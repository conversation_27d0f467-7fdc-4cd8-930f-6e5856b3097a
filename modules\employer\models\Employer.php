<?php

namespace app\modules\employer\models;

use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;

/**
 * Модель работодателя
 * 
 * @property int $id
 * @property string $name
 * @property string $phone
 * @property string $business_name
 * @property string $business_inn
 * @property string $business_address
 * @property int $status
 * @property string $auth_token
 * @property string $token_expires_at
 * @property string $language
 * @property string $created_at
 * @property string $deleted_at
 */
class Employer extends ActiveRecord
{
    // Константы для статуса
    public const STATUS_PENDING = 0;
    public const STATUS_ACTIVE = 1;
    public const STATUS_BLOCKED = 2;

    // Константы для языков
    public const LANGUAGE_UZ = 'uz';
    public const LANGUAGE_RU = 'ru';
    public const LANGUAGE_EN = 'en';
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%employers}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => false,
                'value' => function() {
                    return date('Y-m-d H:i:s');
                }
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name', 'phone'], 'required'],
            [['status'], 'integer'],
            [['created_at', 'deleted_at', 'token_expires_at'], 'safe'],
            [['name'], 'string', 'max' => 100],
            [['phone'], 'string', 'max' => 20],
            [['business_name', 'business_address'], 'string', 'max' => 255],
            [['business_inn'], 'string', 'max' => 20],
            [['auth_token'], 'string', 'max' => 255],
            [['language'], 'string', 'max' => 10],
            [['language'], 'in', 'range' => [self::LANGUAGE_UZ, self::LANGUAGE_RU, self::LANGUAGE_EN]],
            [['language'], 'default', 'value' => self::LANGUAGE_UZ],
            [['status'], 'in', 'range' => [self::STATUS_PENDING, self::STATUS_ACTIVE, self::STATUS_BLOCKED]],
            [['status'], 'default', 'value' => self::STATUS_PENDING],
            [['phone'], 'unique'],
            [['business_inn'], 'unique'],
            [['auth_token'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => 'Имя работодателя',
            'phone' => 'Телефон',
            'business_name' => 'Название компании',
            'business_inn' => 'ИНН',
            'business_address' => 'Адрес компании',
            'status' => 'Статус',
            'auth_token' => 'Токен авторизации',
            'token_expires_at' => 'Срок действия токена',
            'language' => 'Язык',
            'created_at' => 'Дата создания',
            'deleted_at' => 'Дата удаления',
        ];
    }

    /**
     * Получение вакансий работодателя
     */
    public function getVacancies()
    {
        return $this->hasMany(Vacancy::class, ['employer_id' => 'id'])
            ->where(['deleted_at' => null]);
    }

    /**
     * Получение избранных работников
     */
    public function getFavoriteWorkers()
    {
        return $this->hasMany(\app\modules\worker\models\Worker::class, ['id' => 'worker_id'])
            ->viaTable('{{%employer_favorites}}', ['employer_id' => 'id'])
            ->where(['{{%employer_favorites}}.deleted_at' => null]);
    }

    /**
     * Проверка на завершенность профиля
     * @return bool
     */
    public function isProfileComplete()
    {
        return !empty($this->name) && 
               !empty($this->phone) && 
               !empty($this->business_name);
    }

    /**
     * Проверка действительности токена
     * @return bool
     */
    public function isTokenValid()
    {
        if (empty($this->auth_token) || empty($this->token_expires_at)) {
            return false;
        }
        
        return strtotime($this->token_expires_at) > time();
    }

    /**
     * Генерация нового токена авторизации
     * @param int $lifetimeHours часы жизни токена
     * @return string
     */
    public function generateAuthToken($lifetimeHours = 168) // 7 дней по умолчанию
    {
        $this->auth_token = \Yii::$app->security->generateRandomString(64);
        $this->token_expires_at = date('Y-m-d H:i:s', time() + ($lifetimeHours * 3600));
        
        return $this->auth_token;
    }

    /**
     * Поиск по токену
     * @param string $token
     * @return static|null
     */
    public static function findByAuthToken($token)
    {
        if (empty($token)) {
            return null;
        }

        return static::find()
            ->where(['auth_token' => $token])
            ->andWhere(['>', 'token_expires_at', date('Y-m-d H:i:s')])
            ->andWhere(['deleted_at' => null])
            ->one();
    }
} 