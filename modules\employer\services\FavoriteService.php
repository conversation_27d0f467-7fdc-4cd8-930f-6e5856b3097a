<?php

namespace app\modules\employer\services;

use app\modules\employer\models\EmployerFavorite;
use app\modules\worker\models\Worker;
use yii\db\Query;

/**
 * Сервис для управления избранными работниками
 */
class FavoriteService
{
    /**
     * Добавить работника в избранные
     * 
     * @param int $employerId
     * @param int $workerId
     * @return array
     */
    public function addToFavorites($employerId, $workerId)
    {
        // Проверяем, существует ли работник
        $worker = Worker::find()
            ->where(['id' => $workerId, 'deleted_at' => null])
            ->one();

        if (!$worker) {
            return [
                'success' => false,
                'message' => 'Worker not found',
                'code' => 'WORKER_NOT_FOUND'
            ];
        }

        // Проверяем, завершен ли профиль работника
        if ($worker->profile_status !== Worker::PROFILE_STATUS_COMPLETE) {
            return [
                'success' => false,
                'message' => 'Worker profile is not complete',
                'code' => 'WORKER_PROFILE_INCOMPLETE'
            ];
        }

        // Добавляем в избранные
        $favorite = EmployerFavorite::addToFavorites($employerId, $workerId);

        if ($favorite) {
            return [
                'success' => true,
                'message' => 'Worker added to favorites successfully',
                'data' => [
                    'id' => $favorite->id,
                    'worker_id' => $workerId,
                    'created_at' => $favorite->created_at
                ]
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to add worker to favorites',
            'code' => 'ADD_FAVORITE_FAILED'
        ];
    }

    /**
     * Удалить работника из избранных
     * 
     * @param int $employerId
     * @param int $workerId
     * @return array
     */
    public function removeFromFavorites($employerId, $workerId)
    {
        $success = EmployerFavorite::removeFromFavorites($employerId, $workerId);

        if ($success) {
            return [
                'success' => true,
                'message' => 'Worker removed from favorites successfully'
            ];
        }

        return [
            'success' => false,
            'message' => 'Worker not found in favorites or already removed',
            'code' => 'FAVORITE_NOT_FOUND'
        ];
    }

    /**
     * Получить список избранных работников
     * 
     * @param int $employerId
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function getFavoritesList($employerId, $page = 1, $limit = 20)
    {
        $query = Worker::find()
            ->alias('w')
            ->with(['professions'])
            ->innerJoin('{{%employer_favorites}} ef', 'ef.worker_id = w.id')
            ->where([
                'ef.employer_id' => $employerId,
                'ef.deleted_at' => null,
                'w.deleted_at' => null
            ])
            ->orderBy('ef.created_at DESC');

        // Подсчет общего количества
        $totalCount = $query->count();

        // Пагинация
        $offset = ($page - 1) * $limit;
        $workers = $query->offset($offset)->limit($limit)->all();

        // Получаем даты добавления в избранные
        $favoriteDates = $this->getFavoriteDates($employerId, array_column($workers, 'id'));

        // Форматирование данных
        $formattedWorkers = [];
        foreach ($workers as $worker) {
            $formatted = $this->formatWorkerForFavorites($worker);
            $formatted['favorite_added_at'] = $favoriteDates[$worker->id] ?? null;
            $formattedWorkers[] = $formatted;
        }

        return [
            'favorites' => $formattedWorkers,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total_count' => $totalCount,
                'total_pages' => ceil($totalCount / $limit),
            ]
        ];
    }

    /**
     * Проверить, находится ли работник в избранных
     * 
     * @param int $employerId
     * @param int $workerId
     * @return bool
     */
    public function isFavorite($employerId, $workerId)
    {
        return EmployerFavorite::isFavorite($employerId, $workerId);
    }

    /**
     * Получить количество избранных работников
     * 
     * @param int $employerId
     * @return int
     */
    public function getFavoritesCount($employerId)
    {
        return EmployerFavorite::findActive()
            ->where(['employer_id' => $employerId])
            ->count();
    }

    /**
     * Массовое удаление из избранных
     * 
     * @param int $employerId
     * @param array $workerIds
     * @return array
     */
    public function bulkRemoveFromFavorites($employerId, $workerIds)
    {
        $removed = 0;
        $failed = 0;

        foreach ($workerIds as $workerId) {
            $result = $this->removeFromFavorites($employerId, $workerId);
            if ($result['success']) {
                $removed++;
            } else {
                $failed++;
            }
        }

        return [
            'success' => true,
            'message' => "Removed {$removed} workers from favorites",
            'data' => [
                'removed_count' => $removed,
                'failed_count' => $failed,
                'total_count' => count($workerIds)
            ]
        ];
    }

    /**
     * Получить избранных работников по профессии
     * 
     * @param int $employerId
     * @param int $professionId
     * @param int $limit
     * @return array
     */
    public function getFavoritesByProfession($employerId, $professionId, $limit = 10)
    {
        $workers = Worker::find()
            ->alias('w')
            ->with(['professions'])
            ->innerJoin('{{%employer_favorites}} ef', 'ef.worker_id = w.id')
            ->innerJoin('{{%worker_professions}} wp', 'wp.worker_id = w.id')
            ->where([
                'ef.employer_id' => $employerId,
                'ef.deleted_at' => null,
                'w.deleted_at' => null,
                'wp.profession_id' => $professionId
            ])
            ->limit($limit)
            ->orderBy('ef.created_at DESC')
            ->all();

        $formattedWorkers = [];
        foreach ($workers as $worker) {
            $formattedWorkers[] = $this->formatWorkerForFavorites($worker);
        }

        return $formattedWorkers;
    }

    /**
     * Получить статистику избранных
     * 
     * @param int $employerId
     * @return array
     */
    public function getFavoritesStatistics($employerId)
    {
        $totalFavorites = $this->getFavoritesCount($employerId);

        // Статистика по профессиям
        $professionStats = (new Query())
            ->select(['p.name_uz', 'p.name_ru', 'p.name_en', 'COUNT(*) as count'])
            ->from('{{%employer_favorites}} ef')
            ->innerJoin('{{%workers}} w', 'w.id = ef.worker_id')
            ->innerJoin('{{%worker_professions}} wp', 'wp.worker_id = w.id')
            ->innerJoin('{{%professions}} p', 'p.id = wp.profession_id')
            ->where([
                'ef.employer_id' => $employerId,
                'ef.deleted_at' => null,
                'w.deleted_at' => null,
                'p.deleted_at' => null
            ])
            ->groupBy(['p.id', 'p.name_uz', 'p.name_ru', 'p.name_en'])
            ->orderBy('count DESC')
            ->limit(5)
            ->all();

        return [
            'total_favorites' => $totalFavorites,
            'top_professions' => $professionStats
        ];
    }

    /**
     * Получить даты добавления в избранные
     * 
     * @param int $employerId
     * @param array $workerIds
     * @return array
     */
    private function getFavoriteDates($employerId, $workerIds)
    {
        $dates = (new Query())
            ->select(['worker_id', 'created_at'])
            ->from('{{%employer_favorites}}')
            ->where([
                'employer_id' => $employerId,
                'worker_id' => $workerIds,
                'deleted_at' => null
            ])
            ->all();

        $result = [];
        foreach ($dates as $date) {
            $result[$date['worker_id']] = $date['created_at'];
        }

        return $result;
    }

    /**
     * Форматирование работника для списка избранных
     * 
     * @param Worker $worker
     * @return array
     */
    private function formatWorkerForFavorites($worker)
    {
        return [
            'id' => $worker->id,
            'name' => $worker->name,
            'age' => $worker->age,
            'experience_years' => $worker->experience_years,
            'professions' => array_map(function($profession) {
                return [
                    'id' => $profession->id,
                    'name_uz' => $profession->name_uz,
                    'name_ru' => $profession->name_ru,
                    'name_en' => $profession->name_en,
                ];
            }, $worker->professions),
            'location' => [
                'lat' => $worker->lat,
                'lng' => $worker->long,
            ],
            'created_at' => $worker->created_at,
        ];
    }
}
