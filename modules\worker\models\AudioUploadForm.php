<?php

namespace app\modules\worker\models;

use yii\base\Model;
use yii\web\UploadedFile;

/**
 * Форм-модель для загрузки аудиофайла
 */
class AudioUploadForm extends Model
{
    /**
     * @var UploadedFile
     */
    public $audio;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['audio'], 'required'],
            [['audio'], 'file', 
                'skipOnEmpty' => false,
                'extensions' => ['mp3', 'wav', 'ogg', 'm4a', 'aac'],
                'mimeTypes' => ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/mp4', 'audio/aac'],
                'maxSize' => 10 * 1024 * 1024, // 10MB
                'tooBig' => \Yii::t('app', 'worker.audio_file_too_large'),
                'wrongExtension' => \Yii::t('app', 'worker.audio_file_wrong_extension'),
                'wrongMimeType' => \Yii::t('app', 'worker.audio_file_wrong_mime_type')
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'audio' => \Yii::t('app', 'worker.audio_file'),
        ];
    }

    /**
     * Загрузить файл из запроса
     */
    public function loadFile()
    {
        $this->audio = UploadedFile::getInstanceByName('audio');
        return $this->audio !== null;
    }

    /**
     * Получить информацию о файле
     */
    public function getFileInfo()
    {
        if (!$this->audio) {
            return null;
        }

        return [
            'name' => $this->audio->name,
            'size' => $this->audio->size,
            'type' => $this->audio->type,
            'extension' => $this->audio->extension,
        ];
    }

    /**
     * Проверить, является ли файл валидным аудиофайлом
     */
    public function validateAudioFile()
    {
        if (!$this->audio) {
            return false;
        }

        // Проверяем mime-type
        $allowedMimeTypes = [
            'audio/mpeg', 
            'audio/mp3', 
            'audio/wav', 
            'audio/ogg', 
            'audio/mp4', 
            'audio/aac',
            'audio/x-m4a'
        ];

        if (!in_array($this->audio->type, $allowedMimeTypes)) {
            return false;
        }

        // Проверяем расширение
        $allowedExtensions = ['mp3', 'wav', 'ogg', 'm4a', 'aac'];
        if (!in_array(strtolower($this->audio->extension), $allowedExtensions)) {
            return false;
        }

        // Проверяем размер (максимум 10MB)
        if ($this->audio->size > 10 * 1024 * 1024) {
            return false;
        }

        return true;
    }

    /**
     * Получить человекочитаемый размер файла
     */
    public function getFormattedFileSize()
    {
        if (!$this->audio) {
            return '';
        }

        $bytes = $this->audio->size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }
} 