# Тестирование Worker API через Postman

## Настройка окружения

### 1. Создайте новое окружение в Postman
- Название: `Worker API Local`
- Переменные:
  - `base_url` = `http://localhost/ish_top` (или ваш локальный URL)
  - `access_token` = (заполнится автоматически после авторизации)
  - `worker_id` = (заполнится автоматически)
  - `test_phone` = `+998901234567` (ваш тестовый номер)

### 2. Базовые заголовки для всех запросов
```
Content-Type: application/json
Accept: application/json
Accept-Language: ru
```

---

## Последовательность тестирования

### 1. Аутентификация

#### 1.1 Отправка SMS кода
**POST** `{{base_url}}/worker/auth/send-code`

**Headers:**
```
Content-Type: application/json
Accept: application/json
Accept-Language: ru
```

**Body (JSON):**
```json
{
  "phone": "{{test_phone}}"
}
```

**Tests (вкладка Tests в Postman):**
```javascript
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

pm.test("Response has success field", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData).to.have.property('success');
    pm.expect(jsonData.success).to.be.true;
});

pm.test("Response contains phone and expires_in", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData.data).to.have.property('phone');
    pm.expect(jsonData.data).to.have.property('expires_in');
    pm.expect(jsonData.data.expires_in).to.eql(300);
});
```

#### 1.2 Авторизация с кодом
**POST** `{{base_url}}/worker/auth/login`

**Headers:**
```
Content-Type: application/json
Accept: application/json
Accept-Language: ru
```

**Body (JSON):**
```json
{
  "phone": "{{test_phone}}",
  "code": "1234"
}
```

**Tests:**
```javascript
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

pm.test("Response contains access_token", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData.data).to.have.property('access_token');
    
    // Сохраняем токен в переменной окружения
    pm.environment.set("access_token", jsonData.data.access_token);
});

pm.test("Response contains worker data", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData.data).to.have.property('worker');
    pm.expect(jsonData.data.worker).to.have.property('id');
    
    // Сохраняем ID работника
    pm.environment.set("worker_id", jsonData.data.worker.id);
});
```

#### 1.3 Проверка токена
**GET** `{{base_url}}/worker/auth/verify`

**Headers:**
```
Content-Type: application/json
Accept: application/json
Accept-Language: ru
Authorization: Bearer {{access_token}}
```

**Tests:**
```javascript
pm.test("Token is valid", function () {
    pm.response.to.have.status(200);
    var jsonData = pm.response.json();
    pm.expect(jsonData.data.valid).to.be.true;
});
```

### 2. Профиль

#### 2.1 Просмотр профиля
**GET** `{{base_url}}/worker/profile/index`

**Headers:**
```
Content-Type: application/json
Accept: application/json
Accept-Language: ru
Authorization: Bearer {{access_token}}
```

**Tests:**
```javascript
pm.test("Profile retrieved successfully", function () {
    pm.response.to.have.status(200);
    var jsonData = pm.response.json();
    pm.expect(jsonData.data).to.have.property('worker');
    pm.expect(jsonData.data.worker).to.have.property('phone');
});
```

#### 2.2 Обновление профиля
**PUT** `{{base_url}}/worker/profile/update`

**Headers:**
```
Content-Type: application/json
Accept: application/json
Accept-Language: ru
Authorization: Bearer {{access_token}}
```

**Body (JSON):**
```json
{
  "name": "Тестовый Пользователь",
  "age": 25,
  "about": "Описание профиля тестового пользователя",
  "profession_ids": [1, 2]
}
```

**Tests:**
```javascript
pm.test("Profile updated successfully", function () {
    pm.response.to.have.status(200);
    var jsonData = pm.response.json();
    pm.expect(jsonData.data.worker.name).to.eql("Тестовый Пользователь");
    pm.expect(jsonData.data.worker.age).to.eql(25);
});
```

#### 2.3 Загрузка аудио (если требуется)
**POST** `{{base_url}}/worker/profile/upload-audio`

**Headers:**
```
Accept: application/json
Accept-Language: ru
Authorization: Bearer {{access_token}}
```

**Body:** `form-data`
- `audio`: выберите аудио файл

### 3. Вакансии

#### 3.1 Список профессий
**GET** `{{base_url}}/worker/vacancy/professions`

**Headers:**
```
Content-Type: application/json
Accept: application/json
Accept-Language: ru
```

**Tests:**
```javascript
pm.test("Professions list retrieved", function () {
    pm.response.to.have.status(200);
    var jsonData = pm.response.json();
    pm.expect(jsonData.data).to.be.an('array');
    
    if (jsonData.data.length > 0) {
        pm.expect(jsonData.data[0]).to.have.property('id');
        pm.expect(jsonData.data[0]).to.have.property('name_ru');
    }
});
```

#### 3.2 Список вакансий
**GET** `{{base_url}}/worker/vacancy/list?page=1&per_page=10`

**Headers:**
```
Content-Type: application/json
Accept: application/json
Accept-Language: ru
Authorization: Bearer {{access_token}}
```

**Tests:**
```javascript
pm.test("Vacancies list retrieved", function () {
    pm.response.to.have.status(200);
    var jsonData = pm.response.json();
    pm.expect(jsonData.data).to.have.property('items');
    pm.expect(jsonData.data).to.have.property('total');
    pm.expect(jsonData.data).to.have.property('page');
});
```

#### 3.3 Поиск вакансий
**GET** `{{base_url}}/worker/vacancy/search?query=программист&page=1&per_page=10`

**Headers:**
```
Content-Type: application/json
Accept: application/json
Accept-Language: ru
Authorization: Bearer {{access_token}}
```

#### 3.4 Детали вакансии
**GET** `{{base_url}}/worker/vacancy/detail/1`

**Headers:**
```
Content-Type: application/json
Accept: application/json
Accept-Language: ru
Authorization: Bearer {{access_token}}
```

### 4. Избранное (если реализовано)

#### 4.1 Добавить в избранное
**POST** `{{base_url}}/worker/profile/add-favorite`

**Headers:**
```
Content-Type: application/json
Accept: application/json
Accept-Language: ru
Authorization: Bearer {{access_token}}
```

**Body (JSON):**
```json
{
  "vacancy_id": 1
}
```

#### 4.2 Список избранных
**GET** `{{base_url}}/worker/profile/favorites?page=1&per_page=10`

**Headers:**
```
Content-Type: application/json
Accept: application/json
Accept-Language: ru
Authorization: Bearer {{access_token}}
```

#### 4.3 Удалить из избранного
**POST** `{{base_url}}/worker/profile/remove-favorite`

**Headers:**
```
Content-Type: application/json
Accept: application/json
Accept-Language: ru
Authorization: Bearer {{access_token}}
```

**Body (JSON):**
```json
{
  "vacancy_id": 1
}
```

### 5. Выход
**POST** `{{base_url}}/worker/auth/logout`

**Headers:**
```
Content-Type: application/json
Accept: application/json
Accept-Language: ru
Authorization: Bearer {{access_token}}
```

---

## Тестирование ошибок

### Тест без авторизации
Попробуйте любой защищенный endpoint без заголовка `Authorization` - должны получить 401 ошибку.

### Тест с невалидными данными
**POST** `{{base_url}}/worker/auth/send-code`
```json
{
  "phone": ""
}
```
Ожидаемый результат: 422 ошибка валидации.

### Тест rate limiting
Отправьте запрос `send-code` несколько раз подряд - должны получить 429 ошибку.

---

## Сценарии тестирования

### Сценарий 1: Полная регистрация нового пользователя
1. Отправить код
2. Авторизоваться
3. Посмотреть профиль
4. Обновить профиль
5. Просмотреть список вакансий

### Сценарий 2: Поиск и просмотр вакансий
1. Авторизоваться
2. Получить список профессий
3. Найти вакансии по ключевому слову
4. Просмотреть детали конкретной вакансии
5. Добавить вакансию в избранное

### Сценарий 3: Работа с избранными вакансиями
1. Авторизоваться
2. Добавить несколько вакансий в избранное
3. Просмотреть список избранных
4. Удалить вакансию из избранного

---

## Полезные переменные окружения

```
base_url = http://localhost/ish_top
access_token = (автоматически)
worker_id = (автоматически)
test_phone = +998901234567
test_vacancy_id = 1
```

---

## Автоматизация тестов

### Pre-request Script для автоматической авторизации:
```javascript
// Если токен отсутствует, выполнить авторизацию
if (!pm.environment.get("access_token")) {
    console.log("No access token found, need to authenticate first");
}
```

### Общий Test Script для проверки структуры ответа:
```javascript
pm.test("Response is JSON", function () {
    pm.response.to.be.json;
});

pm.test("Response has required structure", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData).to.have.property('success');
    pm.expect(jsonData).to.have.property('data');
    pm.expect(jsonData).to.have.property('message');
});

if (pm.response.code === 401) {
    pm.test("Unauthorized response", function () {
        console.log("Need to authenticate first");
    });
}
```
