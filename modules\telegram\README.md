# 🤖 Telegram Bot для регистрации работников

## 📋 Логика регистрации

Бот проводит работника через пошаговую регистрацию:

### 🔄 Этапы регистрации:

1. **START** (`/start`) - Начало работы с ботом
2. **LANGUAGE** - Выбор языка интерфейса (RU/UZ/EN)
3. **NAME** - Ввод имени работника
4. **PHONE** - Ввод номера телефона
5. **LOCATION** - Отправка геолокации
6. **AGE** - Ввод возраста
7. **PROFESSION** - Выбор профессии через WebView
8. **AUDIO** - Аудиозапись о себе
9. **CONFIRMATION** - Подтверждение или дополнительная информация
10. **COMPLETED** - Завершение и ссылки на приложения

### 🌐 WebView интеграция

После ввода возраста открывается **WebView** с интерфейсом выбора профессий:
- Поиск по названию профессии
- Множественный выбор профессий
- Красивый интерфейс в стиле Telegram
- Автоматическое закрытие после выбора

### 🎤 Обработка аудио

Бот принимает голосовые сообщения и:
- Скачивает аудиофайл через Telegram API
- Сохраняет в папку `web/uploads/audio/`
- Записывает путь в базу данных

### 📱 Ссылки на приложения

После завершения регистрации бот определяет платформу и отправляет соответствующие ссылки:
- **Android**: Google Play Market
- **iOS**: App Store  
- **Web**: Веб-версия приложения

## 🏗️ Архитектура

### Контроллеры:
- `WebhookController` - Основная точка входа для webhook'ов
- `RegistrationController` - Обработка этапов регистрации
- `ProfessionController` - WebView для выбора профессий

### Модели:
- `TemporaryRegistration` - Временная регистрация с этапами
- `TelegramService` - Сервис для работы с Telegram API
- `Worker` - Основная модель работника
- `Profession` - Справочник профессий

## 🔧 Настройка

### 1. Конфигурация бота в `config/web.php`:
```php
'telegramService' => [
    'class' => 'app\modules\telegram\models\TelegramService',
    'botToken' => 'YOUR_BOT_TOKEN', // Токен от @BotFather
],
```

### 2. Установка webhook:
```bash
curl -X POST "https://api.telegram.org/bot{TOKEN}/setWebhook" \
     -H "Content-Type: application/json" \
     -d '{"url": "https://yourdomain.com/telegram/webhook"}'
```

### 3. Права на папки:
```bash
chmod 755 web/uploads/
chmod 755 web/uploads/audio/
```

## 📥 URL маршруты

- `/telegram/webhook/` - Webhook endpoint для Telegram
- `/telegram/profession/select?chat_id=XXX` - WebView выбора профессий
- `/telegram/profession/save` - Сохранение выбранных профессий

## 🗃️ Структура базы данных

### Таблица `temporary_registrations`:
```sql
- chat_id (string) - Telegram chat_id
- name (string) - Имя работника
- phone (string) - Телефон
- age (integer) - Возраст
- lat/long (float) - Координаты
- audio_file_url (string) - Путь к аудиофайлу
- language (string) - Выбранный язык
- status (integer) - Текущий этап регистрации
```

## 🚀 Процесс работы

1. **Пользователь** отправляет `/start`
2. **Бот** предлагает выбрать язык
3. **Пользователь** проходит этапы регистрации
4. **WebView** открывается для выбора профессий
5. **Аудиозапись** сохраняется на сервере
6. **Данные** переносятся в основную таблицу `workers`
7. **Ссылки** на приложения отправляются пользователю

## 🔄 Состояния (Status)

```php
const STEP_START = 0;           // Начало
const STEP_LANGUAGE = 1;        // Выбор языка
const STEP_NAME = 2;            // Ввод имени
const STEP_PHONE = 3;           // Ввод телефона
const STEP_LOCATION = 4;        // Геолокация
const STEP_AGE = 5;             // Возраст
const STEP_PROFESSION = 6;      // WebView профессии
const STEP_AUDIO = 7;           // Аудиозапись
const STEP_CONFIRMATION = 8;    // Подтверждение
const STEP_ADDITIONAL_INFO = 9; // Доп. информация
const STEP_COMPLETED = 10;      // Завершено
```

## 🌍 Мультиязычность

Поддерживаемые языки:
- **🇷🇺 Русский** (`ru`)
- **🇺🇿 Узбекский** (`uz`) 
- **🇬🇧 Английский** (`en`)

Все сообщения бота адаптируются под выбранный язык пользователя.

## 🔐 Безопасность

- Отключен CSRF для webhook'ов
- Валидация входящих данных
- Ограничения на размер аудиофайлов
- Проверка chat_id для доступа к WebView

## 📊 Логирование

Все действия пользователей логируются в категории `telegram`:
```php
\Yii::info('User action description', 'telegram');
```

## 🔄 Расширение функционала

Для добавления новых этапов:
1. Добавить константу в `TemporaryRegistration`
2. Создать обработчик в `RegistrationController`
3. Обновить логику переходов между этапами

## 📈 Система логирования

Модуль использует комплексную систему логирования для отслеживания всех действий пользователей:

### Модели логирования

1. **WorkerLog** (`app\common\models\WorkerLog`) - для логирования действий работников
2. **EmployerLog** (`app\common\models\EmployerLog`) - для логирования действий работодателей

### Типы логируемых событий

#### Для работников:
- `registration_start` - Начало регистрации
- `language_selected` - Выбор языка
- `name_entered` - Ввод имени
- `phone_entered` - Ввод телефона
- `location_sent` - Отправка геолокации
- `age_entered` - Ввод возраста
- `profession_webview_opened` - Открытие WebView выбора профессий
- `profession_selected` - Выбор профессии
- `audio_uploaded` - Загрузка аудиозаписи
- `registration_confirmed` - Подтверждение регистрации
- `registration_completed` - Завершение регистрации
- `message_received` - Получение сообщения
- `callback_received` - Получение callback query
- `error_occurred` - Ошибка

#### Для работодателей:
- `employer_registration_start` - Начало регистрации работодателя
- `vacancy_created` - Создание вакансии
- `vacancy_updated` - Обновление вакансии
- `worker_searched` - Поиск работников
- `worker_viewed` - Просмотр профиля работника
- И другие...

### Использование логирования

```php
// Логирование этапа регистрации
WorkerLog::logRegistrationStep($chatId, TemporaryRegistration::STEP_NAME, [
    'name' => $name,
    'registration_id' => $registration->id
]);

// Логирование входящего сообщения
WorkerLog::logIncomingMessage($chatId, 'text', ['text' => $messageText]);

// Логирование ошибки
WorkerLog::logError($chatId, 'Error message', ['additional_data' => 'value']);

// Логирование действия работодателя
EmployerLog::logAction($chatId, EmployerLog::ACTION_VACANCY_CREATED, [
    'vacancy_id' => $vacancy->id,
    'title' => $vacancy->title
], $employerId);
```

### Получение статистики

```php
// Статистика действий пользователя за 24 часа
$stats = WorkerLog::getActionStats($chatId, '24 HOUR');

// Последние 10 логов
$recentLogs = WorkerLog::getRecentLogs($chatId, 10);

// Логи по конкретному действию
$registrationLogs = WorkerLog::getLogsByAction($chatId, 'registration_start', 5);
```

### Структура лога

Каждая запись в логе содержит:
- `chat_id` - Telegram chat_id пользователя
- `action` - Тип действия (константа)
- `context` - JSON с дополнительными данными
- `worker_id`/`employer_id` - ID пользователя (если доступен)
- `device_info` - Информация об устройстве
- `created_at` - Время действия

### Автоматическое логирование

Система автоматически логирует:
- Все входящие сообщения в `WebhookController`
- Каждый этап регистрации в `RegistrationController`
- Ошибки и исключения
- Callback query от кнопок

Это позволяет полностью отслеживать путь пользователя и анализировать поведение в системе. 