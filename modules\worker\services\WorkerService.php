<?php

namespace app\modules\worker\services;

use app\modules\worker\models\Worker;
use app\modules\worker\models\Profession;
use app\common\enums\VacancyStatus;
use yii\web\UploadedFile;

/**
 * Сервис для работы с профилем работника
 */
class WorkerService
{
    /**
     * Получить профиль работника по номеру телефона
     * 
     * @param string $phone
     * @return array|null
     */
    public function getWorkerProfile($phone)
    {
        $worker = Worker::find()
            ->with(['professions'])
            ->where(['phone' => $phone, 'deleted_at' => null])
            ->one();

        if (!$worker) {
            return null;
        }

        return $this->formatWorkerProfile($worker);
    }

    /**
     * Получить профиль работника по ID
     * 
     * @param int $id
     * @return array|null
     */
    public function getWorkerProfileById($id)
    {
        $worker = Worker::find()
            ->with(['professions'])
            ->where(['id' => $id, 'deleted_at' => null])
            ->one();

        if (!$worker) {
            return null;
        }

        return $this->formatWorkerProfile($worker);
    }

    /**
     * Обновить профиль работника
     * 
     * @param string $phone
     * @param array $data
     * @return array|false
     */
    public function updateWorkerProfile($phone, $data)
    {
        $worker = Worker::findOne(['phone' => $phone, 'deleted_at' => null]);
        
        if (!$worker) {
            return false;
        }

        // Обновляем основные данные
        if (isset($data['name'])) {
            $worker->name = $data['name'];
        }
        
        if (isset($data['age'])) {
            $worker->age = $data['age'];
        }
        
        if (isset($data['experience_years'])) {
            $worker->experience_years = $data['experience_years'];
        }
        
        if (isset($data['about'])) {
            $worker->about = $data['about'];
        }
        
        if (isset($data['language'])) {
            $worker->language = $data['language'];
        }

        // Обновляем местоположение
        if (isset($data['lat']) && isset($data['lng'])) {
            $worker->lat = $data['lat'];
            $worker->long = $data['lng'];
        }

        // Валидация и сохранение
        if (!$worker->validate()) {
            return ['errors' => $worker->errors];
        }

        if (!$worker->save()) {
            return false;
        }

        // Обновляем профессии
        if (isset($data['profession_ids']) && is_array($data['profession_ids'])) {
            $this->updateWorkerProfessions($worker->id, $data['profession_ids']);
        }

        // Проверяем, можно ли пометить профиль как завершенный
        $this->checkAndUpdateProfileStatus($worker);

        return $this->formatWorkerProfile($worker);
    }

    /**
     * Обновить профессии работника
     * 
     * @param int $workerId
     * @param array $professionIds
     */
    private function updateWorkerProfessions($workerId, $professionIds)
    {
        // Удаляем старые связи
        \Yii::$app->db->createCommand()
            ->delete('{{%worker_professions}}', ['worker_id' => $workerId])
            ->execute();

        // Добавляем новые связи
        if (!empty($professionIds)) {
            $rows = [];
            foreach ($professionIds as $professionId) {
                $rows[] = [$workerId, $professionId];
            }
            
            \Yii::$app->db->createCommand()
                ->batchInsert('{{%worker_professions}}', ['worker_id', 'profession_id'], $rows)
                ->execute();
        }
    }

    /**
     * Загрузить аудиофайл
     * 
     * @param string $phone
     * @param UploadedFile $file
     * @return array|false
     */
    public function uploadAudioFile($phone, $file)
    {
        $worker = Worker::findOne(['phone' => $phone, 'deleted_at' => null]);
        
        if (!$worker) {
            return false;
        }

        // Проверяем тип файла
        $allowedTypes = ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg'];
        if (!in_array($file->type, $allowedTypes)) {
            return ['error' => 'Invalid file type'];
        }

        // Проверяем размер файла (максимум 10MB)
        if ($file->size > 10 * 1024 * 1024) {
            return ['error' => 'File too large'];
        }

        // Генерируем уникальное имя файла
        $fileName = 'worker_' . $worker->id . '_' . time() . '.' . $file->extension;
        $uploadPath = \Yii::getAlias('@webroot/uploads/audio/');
        
        // Создаем папку если не существует
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }

        // Сохраняем файл
        $filePath = $uploadPath . $fileName;
        if ($file->saveAs($filePath)) {
            // Обновляем путь в базе данных
            $worker->audio_file_url = '/uploads/audio/' . $fileName;
            $worker->save();
            
            return ['audio_url' => $worker->audio_file_url];
        }

        return false;
    }

    /**
     * Проверить существование работника по номеру телефона
     *
     * @param string $phone
     * @return bool
     */
    public function workerExists($phone)
    {
        return Worker::find()
            ->where(['phone' => $phone, 'deleted_at' => null])
            ->exists();
    }

    /**
     * Создать минимальную запись работника с только номером телефона
     *
     * @param string $phone
     * @return Worker|false
     */
    public function createMinimalWorker($phone)
    {
        // Проверяем, что работник еще не существует
        if ($this->workerExists($phone)) {
            return false;
        }

        $worker = new Worker();
        $worker->phone = $phone;
        $worker->profile_status = Worker::PROFILE_STATUS_INCOMPLETE;
        $worker->created_at = date('Y-m-d H:i:s');

        if ($worker->save()) {
            \Yii::info("Created minimal worker profile for phone: {$phone}", __METHOD__);
            return $worker;
        }

        \Yii::error("Failed to create minimal worker profile for phone: {$phone}. Errors: " . json_encode($worker->errors), __METHOD__);
        return false;
    }

    /**
     * Получить или создать работника по номеру телефона
     *
     * @param string $phone
     * @return Worker|null
     */
    public function getOrCreateWorker($phone)
    {
        // Сначала пытаемся найти существующего работника
        $worker = Worker::findOne(['phone' => $phone, 'deleted_at' => null]);

        if ($worker) {
            return $worker;
        }

        // Если не найден, создаем минимальную запись
        return $this->createMinimalWorker($phone);
    }

    /**
     * Форматировать профиль работника для API
     * 
     * @param Worker $worker
     * @return array
     */
    private function formatWorkerProfile($worker)
    {
        $professions = [];
        foreach ($worker->professions as $profession) {
            $professions[] = [
                'id' => $profession->id,
                'name_ru' => $profession->name_ru,
                'name_uz' => $profession->name_uz,
                'name_en' => $profession->name_en,
            ];
        }

        return [
            'id' => $worker->id,
            'name' => $worker->name,
            'phone' => $worker->phone,
            'age' => $worker->age,
            'experience_years' => $worker->experience_years,
            'about' => $worker->about,
            'language' => $worker->language,
            'location' => [
                'lat' => $worker->lat,
                'lng' => $worker->long,
            ],
            'audio_file_url' => $worker->audio_file_url,
            'professions' => $professions,
            'profile_status' => $worker->profile_status,
            'is_profile_complete' => $worker->isProfileComplete(),
            'is_minimal_profile' => $worker->isMinimalProfile(),
            'created_at' => $worker->created_at,
        ];
    }

    /**
     * Добавить вакансию в избранное
     * 
     * @param int $workerId
     * @param int $vacancyId
     * @return bool
     */
    public function addToFavorites($workerId, $vacancyId)
    {
        // Проверяем, существует ли вакансия
        $vacancy = \app\modules\employer\models\Vacancy::findOne([
            'id' => $vacancyId,
            'deleted_at' => null,
            'status' => VacancyStatus::ACTIVE->value
        ]);
        
        if (!$vacancy) {
            return false;
        }
        
        // Проверяем, нет ли уже такой записи в избранном
        $exists = \Yii::$app->db->createCommand(
            'SELECT COUNT(*) FROM {{%worker_favorites}} 
             WHERE worker_id = :worker_id AND vacancy_id = :vacancy_id AND deleted_at IS NULL'
        )->bindValues([
            ':worker_id' => $workerId,
            ':vacancy_id' => $vacancyId
        ])->queryScalar();

        if ($exists > 0) {
            return true; // Уже в избранном
        }

        // Добавляем в избранное
        try {
            \Yii::$app->db->createCommand()
                ->insert('{{%worker_favorites}}', [
                    'worker_id' => $workerId,
                    'vacancy_id' => $vacancyId,
                    'created_at' => date('Y-m-d H:i:s'),
                    'deleted_at' => null
                ])
                ->execute();
            
            return true;
        } catch (\Exception $e) {
            \Yii::error('Error adding to favorites: ' . $e->getMessage(), 'worker-service');
            return false;
        }
    }

    /**
     * Удалить вакансию из избранного
     * 
     * @param int $workerId
     * @param int $vacancyId
     * @return bool
     */
    public function removeFromFavorites($workerId, $vacancyId)
    {
        try {
            // Помечаем как удаленное (soft delete)
            $affected = \Yii::$app->db->createCommand()
                ->update('{{%worker_favorites}}', 
                    ['deleted_at' => date('Y-m-d H:i:s')],
                    'worker_id = :worker_id AND vacancy_id = :vacancy_id AND deleted_at IS NULL',
                    [':worker_id' => $workerId, ':vacancy_id' => $vacancyId]
                )
                ->execute();
            
            return $affected > 0;
        } catch (\Exception $e) {
            \Yii::error('Error removing from favorites: ' . $e->getMessage(), 'worker-service');
            return false;
        }
    }

    /**
     * Получить список избранных вакансий работника
     * 
     * @param int $workerId
     * @param int $page
     * @param int $perPage
     * @return array
     */
    public function getFavoriteVacancies($workerId, $page = 1, $perPage = 20)
    {
        try {
            // Подсчет общего количества избранных
            $total = \Yii::$app->db->createCommand(
                'SELECT COUNT(*) FROM {{%worker_favorites}} wf
                 JOIN {{%vacancies}} v ON wf.vacancy_id = v.id
                 WHERE wf.worker_id = :worker_id 
                 AND wf.deleted_at IS NULL 
                 AND v.deleted_at IS NULL 
                 AND v.status = :status'
            )->bindValues([
                ':worker_id' => $workerId,
                ':status' => VacancyStatus::ACTIVE->value
            ])->queryScalar();

            // Получаем данные с пагинацией
            $offset = ($page - 1) * $perPage;
            
            $vacancies = \Yii::$app->db->createCommand(
                'SELECT v.*, e.business_name as employer_name, p.name_ru, p.name_uz, p.name_en, wf.created_at as favorited_at
                 FROM {{%worker_favorites}} wf
                 JOIN {{%vacancies}} v ON wf.vacancy_id = v.id
                 JOIN {{%employers}} e ON v.employer_id = e.id
                 JOIN {{%professions}} p ON v.profession_id = p.id
                 WHERE wf.worker_id = :worker_id 
                 AND wf.deleted_at IS NULL 
                 AND v.deleted_at IS NULL 
                 AND v.status = :status
                 ORDER BY wf.created_at DESC
                 LIMIT :limit OFFSET :offset'
            )->bindValues([
                ':worker_id' => $workerId,
                ':status' => VacancyStatus::ACTIVE->value,
                ':limit' => $perPage,
                ':offset' => $offset
            ])->queryAll();

            // Форматируем данные
            $items = [];
            foreach ($vacancies as $vacancy) {
                $items[] = [
                    'id' => $vacancy['id'],
                    'title' => $vacancy['title'],
                    'salary' => $vacancy['salary'],
                    'profession' => [
                        'id' => $vacancy['profession_id'],
                        'name_ru' => $vacancy['name_ru'],
                        'name_uz' => $vacancy['name_uz'],
                        'name_en' => $vacancy['name_en'],
                    ],
                    'employer' => [
                        'id' => $vacancy['employer_id'],
                        'business_name' => $vacancy['employer_name'],
                    ],
                    'location' => [
                        'lat' => $vacancy['lat'],
                        'lng' => $vacancy['long'],
                    ],
                    'created_at' => $vacancy['created_at'],
                    'favorited_at' => $vacancy['favorited_at'],
                ];
            }

            return [
                'items' => $items,
                'total' => $total,
                'page' => $page,
                'per_page' => $perPage
            ];
        } catch (\Exception $e) {
            \Yii::error('Error getting favorite vacancies: ' . $e->getMessage(), 'worker-service');
            return [
                'items' => [],
                'total' => 0,
                'page' => $page,
                'per_page' => $perPage
            ];
        }
    }

    /**
     * Проверить, находится ли вакансия в избранном у работника
     * 
     * @param int $workerId
     * @param int $vacancyId
     * @return bool
     */
    public function isVacancyInFavorites($workerId, $vacancyId)
    {
        $count = \Yii::$app->db->createCommand(
            'SELECT COUNT(*) FROM {{%worker_favorites}} 
             WHERE worker_id = :worker_id AND vacancy_id = :vacancy_id AND deleted_at IS NULL'
        )->bindValues([
            ':worker_id' => $workerId,
            ':vacancy_id' => $vacancyId
        ])->queryScalar();

        return $count > 0;
    }

    /**
     * Проверить и обновить статус профиля работника
     *
     * @param Worker $worker
     */
    private function checkAndUpdateProfileStatus($worker)
    {
        // Критерии для завершенного профиля
        $hasName = !empty($worker->name);
        $hasAge = !empty($worker->age);
        $hasAbout = !empty($worker->about);
        $hasProfessions = count($worker->professions) > 0;

        // Если все основные поля заполнены, помечаем профиль как завершенный
        if ($hasName && $hasAge && $hasAbout && $hasProfessions) {
            if ($worker->profile_status !== Worker::PROFILE_STATUS_COMPLETE) {
                $worker->markProfileComplete();
                \Yii::info("Profile marked as complete for worker ID: {$worker->id}", __METHOD__);
            }
        }
    }
}
