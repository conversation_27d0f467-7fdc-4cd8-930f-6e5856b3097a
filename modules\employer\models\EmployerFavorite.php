<?php

namespace app\modules\employer\models;

use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;

/**
 * Модель избранных работников работодателя
 * 
 * @property int $id
 * @property int $employer_id
 * @property int $worker_id
 * @property string $created_at
 * @property string $deleted_at
 */
class EmployerFavorite extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%employer_favorites}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => false,
                'value' => function() {
                    return date('Y-m-d H:i:s');
                }
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['employer_id', 'worker_id'], 'required'],
            [['employer_id', 'worker_id'], 'integer'],
            [['created_at', 'deleted_at'], 'safe'],
            [['employer_id', 'worker_id'], 'unique', 'targetAttribute' => ['employer_id', 'worker_id'],
                'filter' => ['deleted_at' => null],
                'message' => 'This worker is already in favorites'
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'employer_id' => 'Работодатель',
            'worker_id' => 'Работник',
            'created_at' => 'Дата добавления',
            'deleted_at' => 'Дата удаления',
        ];
    }

    /**
     * Получение работодателя
     */
    public function getEmployer()
    {
        return $this->hasOne(Employer::class, ['id' => 'employer_id']);
    }

    /**
     * Получение работника
     */
    public function getWorker()
    {
        return $this->hasOne(\app\modules\worker\models\Worker::class, ['id' => 'worker_id']);
    }

    /**
     * Мягкое удаление записи
     */
    public function softDelete()
    {
        $this->deleted_at = date('Y-m-d H:i:s');
        return $this->save(false);
    }

    /**
     * Восстановление записи
     */
    public function restore()
    {
        $this->deleted_at = null;
        return $this->save(false);
    }

    /**
     * Поиск активной записи
     */
    public static function findActive()
    {
        return static::find()->where(['deleted_at' => null]);
    }

    /**
     * Добавление в избранные или восстановление если уже существует
     * 
     * @param int $employerId
     * @param int $workerId
     * @return static|null
     */
    public static function addToFavorites($employerId, $workerId)
    {
        // Ищем существующую запись (даже удаленную)
        $favorite = static::find()
            ->where(['employer_id' => $employerId, 'worker_id' => $workerId])
            ->one();

        if ($favorite) {
            if ($favorite->deleted_at !== null) {
                // Восстанавливаем удаленную запись
                $favorite->restore();
            }
            return $favorite;
        }

        // Создаем новую запись
        $favorite = new static();
        $favorite->employer_id = $employerId;
        $favorite->worker_id = $workerId;
        
        if ($favorite->save()) {
            return $favorite;
        }

        return null;
    }

    /**
     * Удаление из избранных
     * 
     * @param int $employerId
     * @param int $workerId
     * @return bool
     */
    public static function removeFromFavorites($employerId, $workerId)
    {
        $favorite = static::findActive()
            ->where(['employer_id' => $employerId, 'worker_id' => $workerId])
            ->one();

        if ($favorite) {
            return $favorite->softDelete();
        }

        return false;
    }

    /**
     * Проверка, находится ли работник в избранных
     * 
     * @param int $employerId
     * @param int $workerId
     * @return bool
     */
    public static function isFavorite($employerId, $workerId)
    {
        return static::findActive()
            ->where(['employer_id' => $employerId, 'worker_id' => $workerId])
            ->exists();
    }
}
