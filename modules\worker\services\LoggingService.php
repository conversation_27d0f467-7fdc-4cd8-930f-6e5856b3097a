<?php

namespace app\modules\worker\services;

use app\modules\worker\models\Worker;
use app\common\models\WorkerLog;

/**
 * Сервис логирования действий работников
 */
class LoggingService
{
    /**
     * Логировать авторизацию работника
     * 
     * @param Worker $worker
     * @param string $phone
     * @param string $tokenExpiresAt
     */
    public function logLogin(Worker $worker, string $phone, string $tokenExpiresAt)
    {
        WorkerLog::logAction(
            $worker->chat_id,
            'api_login',
            [
                'phone' => $phone,
                'token_expires_at' => $tokenExpiresAt
            ],
            $worker->id
        );
    }

    /**
     * Логировать выход работника из системы
     * 
     * @param Worker $worker
     */
    public function logLogout(Worker $worker)
    {
        WorkerLog::logAction(
            $worker->chat_id,
            'api_logout',
            ['token_cleared' => true],
            $worker->id
        );
    }

    /**
     * Логировать обновление токена
     * 
     * @param Worker $worker
     * @param string $oldToken
     * @param string $newExpiresAt
     */
    public function logTokenRefresh(Worker $worker, string $oldToken, string $newExpiresAt)
    {
        WorkerLog::logAction(
            $worker->chat_id,
            'api_token_refresh',
            [
                'old_token' => substr($oldToken, 0, 10) . '...',
                'new_expires_at' => $newExpiresAt
            ],
            $worker->id
        );
    }

    /**
     * Логировать просмотр профиля
     * 
     * @param Worker $worker
     */
    public function logProfileView(Worker $worker)
    {
        WorkerLog::logAction(
            $worker->chat_id,
            'profile_view',
            ['worker_id' => $worker->id],
            $worker->id
        );
    }

    /**
     * Логировать обновление профиля
     * 
     * @param Worker $worker
     * @param array $updatedFields
     */
    public function logProfileUpdate(Worker $worker, array $updatedFields)
    {
        WorkerLog::logAction(
            $worker->chat_id,
            'profile_update',
            [
                'updated_fields' => $updatedFields,
                'worker_id' => $worker->id
            ],
            $worker->id
        );
    }

    /**
     * Логировать загрузку аудиофайла
     * 
     * @param Worker $worker
     * @param array $fileInfo
     * @param string $audioUrl
     */
    public function logAudioUpload(Worker $worker, array $fileInfo, string $audioUrl)
    {
        WorkerLog::logAction(
            $worker->chat_id,
            'audio_upload',
            [
                'file_name' => $fileInfo['name'],
                'file_size' => $fileInfo['size'],
                'file_size_formatted' => $fileInfo['size_formatted'],
                'audio_url' => $audioUrl
            ],
            $worker->id
        );
    }

    /**
     * Логировать добавление/удаление вакансии в избранное
     * 
     * @param Worker $worker
     * @param int $vacancyId
     * @param string $action - 'added' или 'removed'
     */
    public function logVacancyFavorite(Worker $worker, int $vacancyId, string $action)
    {
        WorkerLog::logAction(
            $worker->chat_id,
            'vacancy_favorited',
            [
                'vacancy_id' => $vacancyId,
                'action' => $action
            ],
            $worker->id
        );
    }

    /**
     * Логировать просмотр списка избранных вакансий
     * 
     * @param Worker $worker
     * @param int $totalFavorites
     * @param int $page
     */
    public function logFavoritesView(Worker $worker, int $totalFavorites, int $page)
    {
        WorkerLog::logAction(
            $worker->chat_id,
            'favorites_viewed',
            [
                'total_favorites' => $totalFavorites,
                'page' => $page
            ],
            $worker->id
        );
    }

    /**
     * Логировать поиск вакансий
     * 
     * @param Worker $worker
     * @param string $query
     * @param array $filters
     * @param int $resultsCount
     */
    public function logVacancySearch(Worker $worker, string $query, array $filters, int $resultsCount)
    {
        WorkerLog::logAction(
            $worker->chat_id,
            'vacancy_search',
            [
                'query' => $query,
                'filters' => $filters,
                'results_count' => $resultsCount
            ],
            $worker->id
        );
    }

    /**
     * Логировать просмотр детальной информации о вакансии
     * 
     * @param Worker $worker
     * @param int $vacancyId
     * @param string $vacancyTitle
     */
    public function logVacancyView(Worker $worker, int $vacancyId, string $vacancyTitle)
    {
        WorkerLog::logAction(
            $worker->chat_id,
            'vacancy_view',
            [
                'vacancy_id' => $vacancyId,
                'vacancy_title' => $vacancyTitle
            ],
            $worker->id
        );
    }

    /**
     * Универсальный метод для логирования любого действия
     * 
     * @param Worker|null $worker
     * @param string $action
     * @param array $data
     * @param int|null $chatId - если worker не передан
     * @param int|null $workerId - если worker не передан
     */
    public function logAction(?Worker $worker, string $action, array $data = [], ?int $chatId = null, ?int $workerId = null)
    {
        if ($worker) {
            $chatId = $worker->chat_id;
            $workerId = $worker->id;
        }

        WorkerLog::logAction($chatId, $action, $data, $workerId);
    }
} 