a:54:{s:13:"6879232b53d4f";a:13:{s:3:"tag";s:13:"6879232b53d4f";s:3:"url";s:42:"http://vacanct/worker/list?page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752769323.204991;s:10:"statusCode";i:404;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:6262112;s:14:"processingTime";d:0.16816210746765137;}s:13:"6879234e03ae9";a:13:{s:3:"tag";s:13:"6879234e03ae9";s:3:"url";s:51:"http://vacanct/employer/worker/list?page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752769357.851782;s:10:"statusCode";i:404;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:6359072;s:14:"processingTime";d:0.23013615608215332;}s:13:"687923e7e97e3";a:13:{s:3:"tag";s:13:"687923e7e97e3";s:3:"url";s:51:"http://vacanct/employer/worker/list?page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752769511.722447;s:10:"statusCode";i:404;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:6439120;s:14:"processingTime";d:0.25745606422424316;}s:13:"6879243e39064";a:13:{s:3:"tag";s:13:"6879243e39064";s:3:"url";s:51:"http://vacanct/employer/worker/list?page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752769598.102394;s:10:"statusCode";i:404;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:6360928;s:14:"processingTime";d:0.1781940460205078;}s:13:"6879247fac13b";a:13:{s:3:"tag";s:13:"6879247fac13b";s:3:"url";s:51:"http://vacanct/employer/worker/list?page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752769663.622908;s:10:"statusCode";i:200;s:8:"sqlCount";i:8;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7782280;s:14:"processingTime";d:0.45200610160827637;}s:13:"687927774e9df";a:13:{s:3:"tag";s:13:"687927774e9df";s:3:"url";s:102:"http://vacanct/employer/worker/search?query=%D0%BF%D1%80%D0%BE%D0%B3%D1%80%D0%B0%D0%BC&page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752770423.223043;s:10:"statusCode";i:404;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:6403704;s:14:"processingTime";d:0.1479339599609375;}s:13:"6879282e5a7a6";a:13:{s:3:"tag";s:13:"6879282e5a7a6";s:3:"url";s:102:"http://vacanct/employer/worker/search?query=%D0%BF%D1%80%D0%BE%D0%B3%D1%80%D0%B0%D0%BC&page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752770606.291776;s:10:"statusCode";i:200;s:8:"sqlCount";i:8;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7788344;s:14:"processingTime";d:0.22094106674194336;}s:13:"68792853d430b";a:13:{s:3:"tag";s:13:"68792853d430b";s:3:"url";s:42:"http://vacanct/employer/worker/professions";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752770643.825681;s:10:"statusCode";i:200;s:8:"sqlCount";i:1;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7288344;s:14:"processingTime";d:0.20888400077819824;}s:13:"68792871a178d";a:13:{s:3:"tag";s:13:"68792871a178d";s:3:"url";s:40:"http://vacanct/worker/detail?worker_id=1";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752770673.623853;s:10:"statusCode";i:404;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:6272000;s:14:"processingTime";d:0.13247990608215332;}s:13:"6879287be2320";a:13:{s:3:"tag";s:13:"6879287be2320";s:3:"url";s:49:"http://vacanct/employer/worker/detail?worker_id=1";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752770683.874503;s:10:"statusCode";i:404;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:6413112;s:14:"processingTime";d:0.09092497825622559;}s:13:"68792b0aceda5";a:13:{s:3:"tag";s:13:"68792b0aceda5";s:3:"url";s:42:"http://vacanct/employer/worker/detail?id=1";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752771338.719691;s:10:"statusCode";i:404;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:6413992;s:14:"processingTime";d:0.1555650234222412;}s:13:"68792ba73305d";a:13:{s:3:"tag";s:13:"68792ba73305d";s:3:"url";s:42:"http://vacanct/employer/worker/detail?id=1";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752771495.130273;s:10:"statusCode";i:200;s:8:"sqlCount";i:14;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8032640;s:14:"processingTime";d:0.4696180820465088;}s:13:"68792bde30c2f";a:13:{s:3:"tag";s:13:"68792bde30c2f";s:3:"url";s:36:"http://vacanct/employer/favorite/add";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752771550.135014;s:10:"statusCode";i:422;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7649256;s:14:"processingTime";d:0.2423999309539795;}s:13:"68792c92e887d";a:13:{s:3:"tag";s:13:"68792c92e887d";s:3:"url";s:36:"http://vacanct/employer/favorite/add";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752771730.857894;s:10:"statusCode";i:500;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7624984;s:14:"processingTime";d:0.5163171291351318;}s:13:"68792cba1ca00";a:13:{s:3:"tag";s:13:"68792cba1ca00";s:3:"url";s:36:"http://vacanct/employer/favorite/add";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752771769.997089;s:10:"statusCode";i:200;s:8:"sqlCount";i:15;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8022176;s:14:"processingTime";d:0.4576551914215088;}s:13:"68792d8be207b";a:13:{s:3:"tag";s:13:"68792d8be207b";s:3:"url";s:53:"http://vacanct/employer/favorite/list?page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752771979.79459;s:10:"statusCode";i:404;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:6452192;s:14:"processingTime";d:0.1546628475189209;}s:13:"68792dd643656";a:13:{s:3:"tag";s:13:"68792dd643656";s:3:"url";s:53:"http://vacanct/employer/favorite/list?page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752772053.885559;s:10:"statusCode";i:200;s:8:"sqlCount";i:15;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8052400;s:14:"processingTime";d:1.7667500972747803;}s:13:"68792e023422f";a:13:{s:3:"tag";s:13:"68792e023422f";s:3:"url";s:39:"http://vacanct/employer/favorite/remove";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752772098.154186;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7877400;s:14:"processingTime";d:0.24477887153625488;}s:13:"68792e0947e5b";a:13:{s:3:"tag";s:13:"68792e0947e5b";s:3:"url";s:53:"http://vacanct/employer/favorite/list?page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752772105.230678;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7906992;s:14:"processingTime";d:0.27851200103759766;}s:13:"68792e2112195";a:13:{s:3:"tag";s:13:"68792e2112195";s:3:"url";s:43:"http://vacanct/employer/favorite/statistics";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752772129.033417;s:10:"statusCode";i:200;s:8:"sqlCount";i:5;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7702176;s:14:"processingTime";d:0.2261190414428711;}s:13:"68792f689d760";a:13:{s:3:"tag";s:13:"68792f689d760";s:3:"url";s:36:"http://vacanct/employer/profile/view";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752772456.54771;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7857688;s:14:"processingTime";d:0.30069994926452637;}s:13:"68792f895af4f";a:13:{s:3:"tag";s:13:"68792f895af4f";s:3:"url";s:38:"http://vacanct/employer/profile/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752772489.333702;s:10:"statusCode";i:500;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7638728;s:14:"processingTime";d:0.19836187362670898;}s:13:"68792fab73f50";a:13:{s:3:"tag";s:13:"68792fab73f50";s:3:"url";s:38:"http://vacanct/employer/profile/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752772523.380885;s:10:"statusCode";i:200;s:8:"sqlCount";i:12;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7937200;s:14:"processingTime";d:0.2802579402923584;}s:13:"68792fb840276";a:13:{s:3:"tag";s:13:"68792fb840276";s:3:"url";s:36:"http://vacanct/employer/profile/view";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752772536.204323;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7857688;s:14:"processingTime";d:0.2967069149017334;}s:13:"6879302b5f606";a:13:{s:3:"tag";s:13:"6879302b5f606";s:3:"url";s:41:"http://vacanct/employer/profile/languages";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752772651.310399;s:10:"statusCode";i:200;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:6245008;s:14:"processingTime";d:0.10087990760803223;}s:13:"6879304012e21";a:13:{s:3:"tag";s:13:"6879304012e21";s:3:"url";s:47:"http://vacanct/employer/profile/change-language";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752772672.041957;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7908504;s:14:"processingTime";d:0.25917983055114746;}s:13:"687b22472a98c";a:13:{s:3:"tag";s:13:"687b22472a98c";s:3:"url";s:36:"http://vacanct/employer/profile/view";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752900162.97466;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7769328;s:14:"processingTime";d:8.4008629322052;}s:13:"687b2251c91e7";a:13:{s:3:"tag";s:13:"687b2251c91e7";s:3:"url";s:38:"http://vacanct/employer/profile/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752900177.771826;s:10:"statusCode";i:200;s:8:"sqlCount";i:12;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7849480;s:14:"processingTime";d:0.42853307723999023;}s:13:"687b24d498eab";a:13:{s:3:"tag";s:13:"687b24d498eab";s:3:"url";s:38:"http://vacanct/employer/profile/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752900820.476854;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7782208;s:14:"processingTime";d:0.5292129516601562;}s:13:"687b268291395";a:13:{s:3:"tag";s:13:"687b268291395";s:3:"url";s:38:"http://vacanct/employer/profile/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752901250.509717;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7790120;s:14:"processingTime";d:0.2682368755340576;}s:13:"687b271f7dfdc";a:13:{s:3:"tag";s:13:"687b271f7dfdc";s:3:"url";s:38:"http://vacanct/employer/profile/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752901407.411327;s:10:"statusCode";i:422;s:8:"sqlCount";i:5;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7561120;s:14:"processingTime";d:0.2441251277923584;}s:13:"687b2729c589e";a:13:{s:3:"tag";s:13:"687b2729c589e";s:3:"url";s:38:"http://vacanct/employer/profile/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752901417.746121;s:10:"statusCode";i:422;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7541712;s:14:"processingTime";d:0.2461869716644287;}s:13:"687b273278681";a:13:{s:3:"tag";s:13:"687b273278681";s:3:"url";s:38:"http://vacanct/employer/profile/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752901426.437402;s:10:"statusCode";i:200;s:8:"sqlCount";i:15;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7911240;s:14:"processingTime";d:0.3019428253173828;}s:13:"687b287feea04";a:13:{s:3:"tag";s:13:"687b287feea04";s:3:"url";s:40:"http://vacanct/employer/auth/verify-code";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752901759.875247;s:10:"statusCode";i:200;s:8:"sqlCount";i:11;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7965128;s:14:"processingTime";d:0.5553619861602783;}s:13:"687b288857227";a:13:{s:3:"tag";s:13:"687b288857227";s:3:"url";s:43:"http://vacanct/vacancy/list?page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752901768.319362;s:10:"statusCode";i:404;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:6192568;s:14:"processingTime";d:1.4184300899505615;}s:13:"687b289395461";a:13:{s:3:"tag";s:13:"687b289395461";s:3:"url";s:52:"http://vacanct/employer/vacancy/list?page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752901779.571475;s:10:"statusCode";i:200;s:8:"sqlCount";i:5;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7739440;s:14:"processingTime";d:0.19701290130615234;}s:13:"687b29bdd0b3a";a:13:{s:3:"tag";s:13:"687b29bdd0b3a";s:3:"url";s:38:"http://vacanct/employer/vacancy/create";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752902077.777932;s:10:"statusCode";i:500;s:8:"sqlCount";i:6;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7808928;s:14:"processingTime";d:0.28189992904663086;}s:13:"687b2c04b7382";a:13:{s:3:"tag";s:13:"687b2c04b7382";s:3:"url";s:38:"http://vacanct/employer/vacancy/create";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752902660.651064;s:10:"statusCode";i:200;s:8:"sqlCount";i:15;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8026648;s:14:"processingTime";d:0.38112592697143555;}s:13:"687b2c36832e0";a:13:{s:3:"tag";s:13:"687b2c36832e0";s:3:"url";s:41:"http://vacanct/employer/vacancy/view?id=1";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752902710.467138;s:10:"statusCode";i:404;s:8:"sqlCount";i:4;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7737480;s:14:"processingTime";d:0.2495889663696289;}s:13:"687b2c3fd4b82";a:13:{s:3:"tag";s:13:"687b2c3fd4b82";s:3:"url";s:52:"http://vacanct/employer/vacancy/list?page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752902719.82433;s:10:"statusCode";i:500;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7865120;s:14:"processingTime";d:0.2430119514465332;}s:13:"687b2c63ddada";a:13:{s:3:"tag";s:13:"687b2c63ddada";s:3:"url";s:38:"http://vacanct/employer/vacancy/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752902755.840391;s:10:"statusCode";i:422;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7705184;s:14:"processingTime";d:0.2123880386352539;}s:13:"687b2c6ccabbc";a:13:{s:3:"tag";s:13:"687b2c6ccabbc";s:3:"url";s:38:"http://vacanct/employer/vacancy/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752902764.77869;s:10:"statusCode";i:422;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7705200;s:14:"processingTime";d:0.19720911979675293;}s:13:"687b2c75c6d8c";a:13:{s:3:"tag";s:13:"687b2c75c6d8c";s:3:"url";s:38:"http://vacanct/employer/vacancy/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752902773.778685;s:10:"statusCode";i:422;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7705184;s:14:"processingTime";d:0.1886138916015625;}s:13:"687b2e115736f";a:13:{s:3:"tag";s:13:"687b2e115736f";s:3:"url";s:38:"http://vacanct/employer/vacancy/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752903185.269219;s:10:"statusCode";i:422;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7705624;s:14:"processingTime";d:0.23155498504638672;}s:13:"687b2e160ad57";a:13:{s:3:"tag";s:13:"687b2e160ad57";s:3:"url";s:38:"http://vacanct/employer/vacancy/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752903190.002496;s:10:"statusCode";i:422;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7705624;s:14:"processingTime";d:0.2190999984741211;}s:13:"687b2e1a2484b";a:13:{s:3:"tag";s:13:"687b2e1a2484b";s:3:"url";s:41:"http://vacanct/employer/vacancy/view?id=1";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752903194.093778;s:10:"statusCode";i:404;s:8:"sqlCount";i:4;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7737920;s:14:"processingTime";d:0.23766183853149414;}s:13:"687b2e1e8b68f";a:13:{s:3:"tag";s:13:"687b2e1e8b68f";s:3:"url";s:52:"http://vacanct/employer/vacancy/list?page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752903198.508743;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7848280;s:14:"processingTime";d:0.27022695541381836;}s:13:"687b2e380af15";a:13:{s:3:"tag";s:13:"687b2e380af15";s:3:"url";s:42:"http://vacanct/employer/vacancy/view?id=11";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752903224.000227;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7844280;s:14:"processingTime";d:0.20969414710998535;}s:13:"687b2e5579a2f";a:13:{s:3:"tag";s:13:"687b2e5579a2f";s:3:"url";s:40:"http://vacanct/employer/vacancy/statuses";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752903253.461421;s:10:"statusCode";i:200;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:5795168;s:14:"processingTime";d:0.0731961727142334;}s:13:"687b2e9511815";a:13:{s:3:"tag";s:13:"687b2e9511815";s:3:"url";s:38:"http://vacanct/employer/vacancy/create";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752903316.938204;s:10:"statusCode";i:200;s:8:"sqlCount";i:15;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8026976;s:14:"processingTime";d:0.3429698944091797;}s:13:"687b2e9c3a4f6";a:13:{s:3:"tag";s:13:"687b2e9c3a4f6";s:3:"url";s:52:"http://vacanct/employer/vacancy/list?page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752903324.181272;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7856392;s:14:"processingTime";d:0.25042295455932617;}s:13:"687b2eb390a5a";a:13:{s:3:"tag";s:13:"687b2eb390a5a";s:3:"url";s:38:"http://vacanct/employer/vacancy/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752903347.561202;s:10:"statusCode";i:422;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7705624;s:14:"processingTime";d:0.17289495468139648;}s:13:"687b2f53a10af";a:13:{s:3:"tag";s:13:"687b2f53a10af";s:3:"url";s:38:"http://vacanct/employer/vacancy/update";s:4:"ajax";i:0;s:6:"method";s:3:"PUT";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752903507.561299;s:10:"statusCode";i:200;s:8:"sqlCount";i:14;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8017096;s:14:"processingTime";d:0.3327291011810303;}s:13:"687b304c5d320";a:13:{s:3:"tag";s:13:"687b304c5d320";s:3:"url";s:38:"http://vacanct/employer/vacancy/delete";s:4:"ajax";i:0;s:6:"method";s:6:"DELETE";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752903756.30223;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7922600;s:14:"processingTime";d:0.25559306144714355;}}