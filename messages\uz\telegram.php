<?php

return [
    // Profession selection
    'profession.select.title' => 'Faoliyat tanlash',
    'profession.select.heading' => 'Faoliyat tanlang',
    'profession.select.description' => 'Bir yoki bir nechta kasb tanlang',
    'profession.select.search_placeholder' => '🔍 Kasb qidirish...',
    'profession.select.no_results' => 'Kasblar topilmadi',
    'profession.select.submit_button' => 'Kasblarni tanlash',
    'profession.select.saving' => 'Saqlanmoqda...',
    'profession.select.select_count' => 'Tanlash ({count})',
    'profession.select.selected_profession' => 'Kasb tanlandi',
    'profession.select.save_error' => 'Saqlashda xatolik',
    'profession.select.connection_error' => 'Ulanish xatosi',
    
    // Registration flow
    'welcome_message' => "👋 Vacant ga xush kelibsiz!\n\n<b>Tilni tanlang:</b>",
    'welcome.back' => "👋 Xush kelibsiz, {name}!\n\n🎯 Sizning ro'yxa<PERSON><PERSON> o'tishingiz allaqachon yakunlangan.",
    'language.selected' => "✅ Til tanlandi: O'zbekcha\n\n👤 <b>Ismingiz nima? Ismingizni kiriting:</b>",
    'name.entered' => "✅ Ism: {name}\n\n📱 <b>Telefon raqamingizni ulashing:</b>",
    'phone.entered' => "✅ Telefon: {phone}\n\n📍 <b>Yaqin atrofdagi ish topish uchun geolokatsiyangizniyuboring:</b>",
    'phone.not_your_own' => "❌ Iltimos, faqat o'zingizning telefon raqamingizni ulashing.",
    'phone.use_button' => "📱 Iltimos, qo'lda kiritish o'rniga <b>\"📱 Telefon raqamini ulashish\"</b> tugmasidan foydalaning.",
    'location.received' => "✅ Geolokatsiya qabul qilindi\n\n🎂 <b>Necha yoshdasiz? Yoshingizni kiriting:</b>",
    'location.received_with_address' => "✅ Geolokatsiya qabul qilindi\n📍 <b>Sizning joylashuvingiz:</b> {address}\n\n🎂 <b>Necha yoshdasiz? Yoshingizni kiriting:</b>",
    'location.received_short' => "qabul qilindi",
    'location.use_button' => "📍 Iltimos, matn kiritish o'rniga <b>\"📍 Geolokatsiya yuborish\"</b> tugmasidan foydalaning.",
    'age.invalid' => "❌ Iltimos, to'g'ri yoshni kiriting (16-80 yosh):",
    'age.entered' => "✅ Yosh: {age} yosh\n\n🔧 <b>Endi faoliyatingizni (kasbingizni) tanlang:</b>",
    'experience.invalid' => "❌ Iltimos, to'g'ri ish tajribasini kiriting (0-50 yil):",
    'experience.entered' => "✅ Ish tajribasi: {experience} {years_text}\n\n🎤 <b>Endi o'zingiz haqida ovozli xabar bilan gapirib bering (60 soniyagacha):</b>",
    'audio.received' => "✅ Audio yozuv qabul qilindi!\n\n🎯 Ro'yxatdan o'tish deyarli yakunlandi. Harakatni tanlang:",
    'audio.failed' => "⚠️ Audio yozuvni saqlashda xatolik, lekin bu majburiy emas.\n\n🎯 Ro'yxatdan o'tishni davom ettiramiz:",
    'registration.completed' => "🎉 Tabriklaymiz! Ro'yxatdan o'tish yakunlandi!\n\n📱 Bizning ilovamizni yuklab oling:\n\n{downloadLinks}",
    'additional.info' => "📝 Ish tajribangiz haqida qo'shimcha ma'lumot kiriting:",
    'professions.selected' => "✅ Kasblar tanlandi: {professionList}\n\n📈 <b>Ish tajribangizni yillarda ko'rsating (masalan: 2):</b>",
    
    // Confirmation message
    'confirm.data' => "📋 <b>Ma'lumotlaringizni tekshiring:</b>\n\n👤 Ism: {name}\n📱 Telefon: {phone}\n🎂 Yosh: {age} yosh\n📍 Lokatsiya: {location}\n🔧 Kasblar: {professions}\n📈 Tajriba: {experience} {years_text}\n🎤 Audio: {audio_status}\n\n<b>Barcha ma'lumotlar to'g'rimi?</b>",
    'audio.uploaded' => 'yuklandi',
    'audio.not_uploaded' => 'yuklanmadi',
    
    // Years declension
    'years.1' => 'yil',
    'years.2_4' => 'yil', 
    'years.other' => 'yil',
    
    // WebApp buttons
    'webapp.select_activity' => '🔧 Faoliyat tanlash',
    'webapp.send_location' => '📍 Geolokatsiya yuborish',
    'webapp.share_phone' => '📱 Telefon raqamini ulashish',
    
    // Buttons
    'button.confirm_registration' => '✅ Ro\'yxatdan o\'tishni tasdiqlash',
    'button.additional_info' => '📝 Qo\'shimcha ma\'lumot to\'ldirish',
    'button.edit_data' => '✏️ Tahrirlash',
    
    // Edit menu and prompts
    'edit.worker_not_found' => '❌ Ishchi ma\'lumotlari topilmadi',
    'edit.menu' => "✏️ <b>Ma\'lumotlarni tahrirlash</b>\n\n👤 Ism: {name}\n\nNimani o'zgartirmoqchisiz?",
    'edit.button.name' => '👤 Ism',
    'edit.button.phone' => '📱 Telefon',
    'edit.button.age' => '🎂 Yosh',
    'edit.button.location' => '📍 Lokatsiya',
    'edit.button.profession' => '🔧 Kasblar',
    'edit.button.experience' => '📈 Tajriba',
    'edit.button.audio' => '🎤 Audio',
    'edit.button.back' => '⬅️ Orqaga',
    
    // Edit prompts
    'edit.name.prompt' => "👤 <b>Ismni o'zgartirish</b>\n\nJoriy ism: <b>{current_name}</b>\n\nYangi ismni kiriting:",
    'edit.phone.prompt' => "📱 <b>Telefonni o'zgartirish</b>\n\nJoriy telefon: <b>{current_phone}</b>\n\nYangi telefon raqamini ulashing:",
    'edit.age.prompt' => "🎂 <b>Yoshni o'zgartirish</b>\n\nJoriy yosh: <b>{current_age} yosh</b>\n\nYangi yoshni kiriting (16-80):",
    'edit.location.prompt' => "📍 <b>Lokatsiyani o'zgartirish</b>\n\nYangi geolokatsiyani yuboring:",
    'edit.profession.prompt' => "🔧 <b>Kasblarni o'zgartirish</b>\n\nYangi kasblarni tanlang:",
    'edit.experience.prompt' => "📈 <b>Ish tajribasini o'zgartirish</b>\n\nJoriy tajriba: <b>{current_experience} yil</b>\n\nYangi ish tajribasini kiriting (0-50):",
    'edit.audio.prompt' => "🎤 <b>Audio biografiyani o'zgartirish</b>\n\nYangi ovozli xabar yuboring (60 soniyagacha):",
    
    // Edit success messages
    'edit.name.success' => "✅ <b>Ism muvaffaqiyatli o'zgartirildi!</b>\n\nYangi ism: <b>{new_name}</b>",
    'edit.phone.success' => "✅ <b>Telefon muvaffaqiyatli o'zgartirildi!</b>\n\nYangi telefon: <b>{new_phone}</b>",
    'edit.age.success' => "✅ <b>Yosh muvaffaqiyatli o'zgartirildi!</b>\n\nYangi yosh: <b>{new_age} yosh</b>",
    'edit.location.success' => "✅ <b>Lokatsiya muvaffaqiyatli o'zgartirildi!</b>",
    'edit.location.success_with_address' => "✅ <b>Lokatsiya muvaffaqiyatli o'zgartirildi!</b>\n\n📍 Yangi manzil: <b>{address}</b>",
    'edit.experience.success' => "✅ <b>Ish tajribasi muvaffaqiyatli o'zgartirildi!</b>\n\nYangi tajriba: <b>{new_experience} {years_text}</b>",
    'edit.audio.success' => "✅ <b>Audio biografiya muvaffaqiyatli yangilandi!</b>",
    'edit.audio.failed' => "⚠️ <b>Audioni yangilashda xatolik</b>, lekin bu muhim emas.",
    
    // Audio validation
    'audio.voice_only' => "🎤 <b>Iltimos, ovozli xabar yuboring</b>, musiqa yoki audio fayl emas.\n\nOvozli xabar yozish uchun Telegramda mikrofon tugmasini bosib turing.",
    
    // Input validation and reminders
    'language.use_button' => "🌐 <b>Iltimos, tilni tanlang</b> yuqoridagi tugmalardan birini bosib.",
    'name.invalid' => "👤 <b>Noto'g'ri ism!</b>\n\nIsm faqat harflar, bo'shliqlar va defislardan iborat bo'lishi kerak (2 dan 50 gacha belgi).\n\nQaytadan urinib ko'ring:",
    'age.numbers_only' => "🎂 <b>Faqat raqamlarni kiriting!</b>\n\nYosh 16 dan 80 gacha raqam bo'lishi kerak.\n\nQaytadan urinib ko'ring:",
    'profession.use_button' => "🔧 <b>Iltimos, kasblarni tanlang</b> yuqoridagi <b>\"🔧 Faoliyat tanlash\"</b> tugmasini bosib.",
    'experience.numbers_only' => "📈 <b>Faqat raqamlarni kiriting!</b>\n\nIsh tajribasi 0 dan 50 gacha raqam bo'lishi kerak.\n\nQaytadan urinib ko'ring:",
    'audio.use_voice' => "🎤 <b>Iltimos, ovozli xabar yuboring</b>\n\nYozish uchun Telegramda mikrofon tugmasini bosib turing.",
    'media.voice_only' => "🎤 <b>Bu bosqichda faqat ovozli xabar kerak</b>\n\nIltimos, rasm, video yoki stikerlar yubormang.",
]; 