<?php

namespace app\modules\employer\controllers;

use app\modules\employer\services\FavoriteService;
use app\modules\employer\services\LoggingService;
use yii\web\Response;

/**
 * Контроллер для управления избранными работниками
 * Все методы требуют авторизации
 */
class FavoriteController extends BaseApiController
{
    /**
     * @var FavoriteService
     */
    private $favoriteService;

    /**
     * @var LoggingService
     */
    private $loggingService;

    /**
     * {@inheritdoc}
     */
    public function init()
    {
        parent::init();
        $this->favoriteService = new FavoriteService();
        $this->loggingService = new LoggingService();
    }

    /**
     * Добавить работника в избранные
     *
     * POST /employer/favorite/add
     *
     * @return Response
     */
    public function actionAdd()
    {
        $employer = $this->requireAuth();
        $workerId = (int)$this->getRequestData('worker_id');

        if (empty($workerId)) {
            return $this->sendValidationError([
                'worker_id' => [$this->t('app', 'Worker ID is required')]
            ]);
        }

        try {
            $result = $this->favoriteService->addToFavorites($employer->id, $workerId);

            if ($result['success']) {
                // Логируем добавление в избранные
                $this->loggingService->logWorkerFavorited($employer, $workerId);

                return $this->sendSuccess($result['data'], $result['message']);
            }

            // Обрабатываем различные типы ошибок
            $statusCode = 400;
            switch ($result['code'] ?? '') {
                case 'WORKER_NOT_FOUND':
                    $statusCode = 404;
                    break;
                case 'WORKER_PROFILE_INCOMPLETE':
                    $statusCode = 422; // Unprocessable Entity
                    break;
            }

            return $this->sendError($result['message'], ['code' => $result['code'] ?? null], $statusCode);

        } catch (\Exception $e) {
            \Yii::error('Error adding worker to favorites: ' . $e->getMessage(), __METHOD__);
            return $this->sendError(
                $this->t('app', 'Failed to add worker to favorites'),
                null,
                500
            );
        }
    }

    /**
     * Удалить работника из избранных
     *
     * DELETE /employer/favorite/remove
     * или
     * POST /employer/favorite/remove
     *
     * @return Response
     */
    public function actionRemove()
    {
        $employer = $this->requireAuth();
        $workerId = (int)$this->getRequestData('worker_id');

        if (empty($workerId)) {
            return $this->sendValidationError([
                'worker_id' => [$this->t('app', 'Worker ID is required')]
            ]);
        }

        try {
            $result = $this->favoriteService->removeFromFavorites($employer->id, $workerId);

            if ($result['success']) {
                // Логируем удаление из избранных
                $this->loggingService->logAction(
                    $employer->id,
                    'worker_unfavorited',
                    ['worker_id' => $workerId]
                );

                return $this->sendSuccess(null, $result['message']);
            }

            return $this->sendError($result['message'], ['code' => $result['code'] ?? null], 400);

        } catch (\Exception $e) {
            \Yii::error('Error removing worker from favorites: ' . $e->getMessage(), __METHOD__);
            return $this->sendError(
                $this->t('app', 'Failed to remove worker from favorites'),
                null,
                500
            );
        }
    }

    /**
     * Получить список избранных работников
     *
     * GET /employer/favorite/index
     *
     * @return Response
     */
    public function actionIndex()
    {
        $employer = $this->requireAuth();
        $page = (int)($this->getRequestData('page') ?? 1);
        $limit = min((int)($this->getRequestData('limit') ?? 20), 50);

        try {
            $result = $this->favoriteService->getFavoritesList($employer->id, $page, $limit);

            // Логируем просмотр избранных
            $this->loggingService->logAction(
                $employer->id,
                'favorites_viewed',
                ['page' => $page, 'count' => count($result['favorites'])]
            );

            return $this->sendSuccess($result, $this->t('app', 'Favorites list retrieved successfully'));

        } catch (\Exception $e) {
            \Yii::error('Error retrieving favorites list: ' . $e->getMessage(), __METHOD__);
            return $this->sendError(
                $this->t('app', 'Failed to retrieve favorites list'),
                null,
                500
            );
        }
    }

    /**
     * Проверить, находится ли работник в избранных
     *
     * GET /employer/favorite/check
     *
     * @return Response
     */
    public function actionCheck()
    {
        $employer = $this->requireAuth();
        $workerId = (int)$this->getRequestData('worker_id');

        if (empty($workerId)) {
            return $this->sendValidationError([
                'worker_id' => [$this->t('app', 'Worker ID is required')]
            ]);
        }

        try {
            $isFavorite = $this->favoriteService->isFavorite($employer->id, $workerId);

            return $this->sendSuccess([
                'worker_id' => $workerId,
                'is_favorite' => $isFavorite
            ]);

        } catch (\Exception $e) {
            \Yii::error('Error checking favorite status: ' . $e->getMessage(), __METHOD__);
            return $this->sendError(
                $this->t('app', 'Failed to check favorite status'),
                null,
                500
            );
        }
    }

    /**
     * Массовое удаление из избранных
     *
     * POST /employer/favorite/bulk-remove
     *
     * @return Response
     */
    public function actionBulkRemove()
    {
        $employer = $this->requireAuth();
        $workerIds = $this->getRequestData('worker_ids');

        if (empty($workerIds) || !is_array($workerIds)) {
            return $this->sendValidationError([
                'worker_ids' => [$this->t('app', 'Worker IDs array is required')]
            ]);
        }

        // Валидируем что все элементы массива - числа
        $workerIds = array_filter($workerIds, function($id) {
            return is_numeric($id) && $id > 0;
        });

        if (empty($workerIds)) {
            return $this->sendValidationError([
                'worker_ids' => [$this->t('app', 'Valid worker IDs are required')]
            ]);
        }

        try {
            $result = $this->favoriteService->bulkRemoveFromFavorites($employer->id, $workerIds);

            // Логируем массовое удаление
            $this->loggingService->logAction(
                $employer->id,
                'bulk_favorites_removed',
                [
                    'worker_ids' => $workerIds,
                    'count' => count($workerIds),
                    'removed_count' => $result['data']['removed_count']
                ]
            );

            return $this->sendSuccess($result['data'], $result['message']);

        } catch (\Exception $e) {
            \Yii::error('Error bulk removing favorites: ' . $e->getMessage(), __METHOD__);
            return $this->sendError(
                $this->t('app', 'Failed to remove workers from favorites'),
                null,
                500
            );
        }
    }

    /**
     * Получить избранных работников по профессии
     *
     * GET /employer/favorite/by-profession
     *
     * @return Response
     */
    public function actionByProfession()
    {
        $employer = $this->requireAuth();
        $professionId = (int)$this->getRequestData('profession_id');
        $limit = min((int)($this->getRequestData('limit') ?? 10), 20);

        if (empty($professionId)) {
            return $this->sendValidationError([
                'profession_id' => [$this->t('app', 'Profession ID is required')]
            ]);
        }

        try {
            $workers = $this->favoriteService->getFavoritesByProfession($employer->id, $professionId, $limit);

            return $this->sendSuccess([
                'workers' => $workers,
                'profession_id' => $professionId,
                'count' => count($workers)
            ], $this->t('app', 'Favorite workers by profession retrieved successfully'));

        } catch (\Exception $e) {
            \Yii::error('Error retrieving favorite workers by profession: ' . $e->getMessage(), __METHOD__);
            return $this->sendError(
                $this->t('app', 'Failed to retrieve favorite workers by profession'),
                null,
                500
            );
        }
    }

    /**
     * Получить статистику избранных
     *
     * GET /employer/favorite/statistics
     *
     * @return Response
     */
    public function actionStatistics()
    {
        $employer = $this->requireAuth();

        try {
            $statistics = $this->favoriteService->getFavoritesStatistics($employer->id);

            return $this->sendSuccess($statistics, $this->t('app', 'Favorites statistics retrieved successfully'));

        } catch (\Exception $e) {
            \Yii::error('Error retrieving favorites statistics: ' . $e->getMessage(), __METHOD__);
            return $this->sendError(
                $this->t('app', 'Failed to retrieve favorites statistics'),
                null,
                500
            );
        }
    }

    /**
     * Получить количество избранных
     *
     * GET /employer/favorite/count
     *
     * @return Response
     */
    public function actionCount()
    {
        $employer = $this->requireAuth();

        try {
            $count = $this->favoriteService->getFavoritesCount($employer->id);

            return $this->sendSuccess([
                'count' => $count
            ]);

        } catch (\Exception $e) {
            \Yii::error('Error retrieving favorites count: ' . $e->getMessage(), __METHOD__);
            return $this->sendError(
                $this->t('app', 'Failed to retrieve favorites count'),
                null,
                500
            );
        }
    }
}
