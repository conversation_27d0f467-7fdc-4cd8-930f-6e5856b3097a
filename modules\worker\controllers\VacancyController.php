<?php

namespace app\modules\worker\controllers;

use app\modules\worker\services\VacancyService;
use app\modules\worker\services\LoggingService;
use app\modules\worker\filters\AuthFilter;
use app\modules\worker\models\Worker;
use yii\web\Response;

/**
 * Контроллер для работы с вакансиями в модуле worker
 */
class VacancyController extends BaseApiController
{
    /**
     * @var VacancyService
     */
    private $vacancyService;

    /**
     * @var LoggingService
     */
    private $loggingService;

    /**
     * @var Worker|null Текущий авторизованный работник
     */
    public $currentWorker;

    /**
     * {@inheritdoc}
     */
    public function init()
    {
        parent::init();
        $this->vacancyService = new VacancyService();
        $this->loggingService = new LoggingService();
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        // Настройка авторизации
        $behaviors['auth'] = [
            'class' => AuthFilter::class,
            'publicActions' => [
                'vacancy/list',    // Публичный доступ к списку вакансий
                'vacancy/search',   // Публичный доступ к поиску вакансий
                'vacancy/professions'  // Публичный доступ к списку профессий
            ],
            'authRequired' => [
                'vacancy/detail'   // Требует авторизации для детального просмотра
            ]
        ];

        return $behaviors;
    }

    /**
     * {@inheritdoc}
     */
    public function beforeAction($action)
    {
        if (!parent::beforeAction($action)) {
            return false;
        }

        // Проверяем завершенность профиля для авторизованных пользователей
        if ($this->currentWorker) {
            $profileCheckResult = $this->checkProfileCompletion($this->currentWorker);
            if ($profileCheckResult !== null) {
                // Отправляем ответ и прерываем выполнение
                $profileCheckResult->send();
                \Yii::$app->end();
            }
        }

        return true;
    }

    /**
     * Получить список вакансий
     * 
     * GET /worker/vacancy/list
     * 
     * @return Response
     */
    public function actionList()
    {
        $request = \Yii::$app->request;
        
        // Параметры пагинации
        $page = (int) $request->get('page', 1);
        $perPage = min((int) $request->get('per_page', 20), 50); // Максимум 50 элементов на страницу

        // Фильтры
        $filters = [];
        
        if ($request->get('profession_id')) {
            $filters['profession_id'] = (int) $request->get('profession_id');
        }
        
        if ($request->get('min_salary')) {
            $filters['min_salary'] = (int) $request->get('min_salary');
        }
        
        // Фильтр по местоположению
        if ($request->get('lat') && $request->get('lng') && $request->get('radius')) {
            $filters['lat'] = (float) $request->get('lat');
            $filters['lng'] = (float) $request->get('lng');
            $filters['radius'] = (int) $request->get('radius');
        }

        try {
            $result = $this->vacancyService->getVacanciesList($page, $perPage, $filters);
            
            return $this->sendPaginated(
                $result['items'],
                $result['total'],
                $result['page'],
                $result['per_page'],
                $this->t('app', 'Vacancies retrieved successfully')
            );
        } catch (\Exception $e) {
            \Yii::error('Error getting vacancies list: ' . $e->getMessage(), 'worker-api');
            return $this->sendError($this->t('app', 'Error retrieving vacancies'));
        }
    }

    /**
     * Поиск вакансий
     * 
     * GET /worker/vacancy/search
     * 
     * @return Response
     */
    public function actionSearch()
    {
        $request = \Yii::$app->request;
        
        // Поисковый запрос
        $query = trim($request->get('query', ''));
        
        // Параметры пагинации
        $page = (int) $request->get('page', 1);
        $perPage = min((int) $request->get('per_page', 20), 50);

        // Фильтры (те же что и в списке)
        $filters = [];
        
        if ($request->get('profession_id')) {
            $filters['profession_id'] = (int) $request->get('profession_id');
        }
        
        if ($request->get('min_salary')) {
            $filters['min_salary'] = (int) $request->get('min_salary');
        }
        
        if ($request->get('lat') && $request->get('lng') && $request->get('radius')) {
            $filters['lat'] = (float) $request->get('lat');
            $filters['lng'] = (float) $request->get('lng');
            $filters['radius'] = (int) $request->get('radius');
        }

        try {
            $result = $this->vacancyService->searchVacancies($query, $page, $perPage, $filters);
            
            // Логируем поисковый запрос
            if ($this->currentWorker) {
                $this->loggingService->logVacancySearch($this->currentWorker, $query, $filters, $result['total']);
            }
            
            return $this->sendPaginated(
                $result['items'],
                $result['total'],
                $result['page'],
                $result['per_page'],
                $this->t('app', 'Search completed successfully')
            );
        } catch (\Exception $e) {
            \Yii::error('Error searching vacancies: ' . $e->getMessage(), 'worker-api');
            return $this->sendError($this->t('app', 'Error searching vacancies'));
        }
    }

    /**
     * Получить детальную информацию о вакансии
     * 
     * GET /worker/vacancy/detail/{id}
     * 
     * @param int $id
     * @return Response
     */
    public function actionDetail($id)
    {
        if (!is_numeric($id) || $id <= 0) {
            return $this->sendValidationError([
                'id' => [$this->t('app', 'Invalid vacancy ID')]
            ]);
        }

        try {
            $vacancy = $this->vacancyService->getVacancyDetail((int) $id);
            
            if (!$vacancy) {
                return $this->sendNotFound($this->t('app', 'Vacancy not found'));
            }

            // Логируем просмотр вакансии
            if ($this->currentWorker) {
                $this->loggingService->logVacancyView($this->currentWorker, $id, $vacancy['title']);
            }
            
            return $this->sendSuccess(
                $vacancy,
                $this->t('app', 'Vacancy details retrieved successfully')
            );
        } catch (\Exception $e) {
            \Yii::error('Error getting vacancy detail: ' . $e->getMessage(), 'worker-api');
            return $this->sendError($this->t('app', 'Error retrieving vacancy details'));
        }
    }

    /**
     * Получить список всех профессий
     * 
     * GET /worker/vacancy/professions
     * 
     * @return Response
     */
    public function actionProfessions()
    {
        try {
            $professions = \app\modules\worker\models\Profession::find()
                ->where(['deleted_at' => null])
                ->orderBy(['name_ru' => SORT_ASC])
                ->all();
            
            $items = [];
            foreach ($professions as $profession) {
                $items[] = [
                    'id' => $profession->id,
                    'name_ru' => $profession->name_ru,
                    'name_uz' => $profession->name_uz,
                    'name_en' => $profession->name_en,
                ];
            }
            
            return $this->sendSuccess(
                $items,
                $this->t('app', 'Professions list retrieved successfully')
            );
        } catch (\Exception $e) {
            \Yii::error('Error getting professions list: ' . $e->getMessage(), 'worker-api');
            return $this->sendError($this->t('app', 'Error retrieving professions list'));
        }
    }
}
