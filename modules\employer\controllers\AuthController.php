<?php

namespace app\modules\employer\controllers;

use app\modules\employer\services\EmployerService;
use app\modules\employer\services\LoggingService;
use app\common\services\ApiResponse;
use app\common\services\SmsService;
use yii\web\Response;

/**
 * Контроллер авторизации для модуля employer
 */
class AuthController extends BaseApiController
{
    private const SMS_CODE_COOLDOWN = 120; // 2 минуты в секундах
    private const SMS_CODE_LIFETIME = 300; // 5 минут в секундах
    private const SMS_CODE_LENGTH = 4;
    private const AUTH_TOKEN_LIFETIME_HOURS = 24 * 7; // 7 дней в часах

    /**
     * @var EmployerService
     */
    private $employerService;

    /**
     * @var LoggingService
     */
    private $loggingService;

    /**
     * @var SmsService
     */
    private $smsService;

    /**
     * {@inheritdoc}
     */
    public function init()
    {
        parent::init();
        $this->employerService = new EmployerService();
        $this->loggingService = new LoggingService();
        $this->smsService = new SmsService();
    }

    /**
     * Отправка кода валидации на номер телефона
     *
     * POST /employer/auth/send-code
     *
     * @return Response
     */
    public function actionSendCode()
    {
        $phone = $this->getRequestData('phone');

        // Валидация входных данных
        if (empty($phone)) {
            return $this->sendValidationError([
                'phone' => [$this->t('app', 'Phone number is required')]
            ]);
        }

        // Нормализация номера телефона
        $phone = $this->normalizePhone($phone);

        // Проверяем ограничение на повторную отправку (2 минуты)
        $lastSentKey = 'sms_last_sent_employer_' . md5($phone);
        $lastSentTime = \Yii::$app->cache->get($lastSentKey);

        if ($lastSentTime && (time() - $lastSentTime) < self::SMS_CODE_COOLDOWN) {
            $remainingTime = self::SMS_CODE_COOLDOWN - (time() - $lastSentTime);
            return $this->sendError(
                $this->t('app', 'Please wait {seconds} seconds before requesting a new code', [
                    'seconds' => $remainingTime
                ]),
                ['remaining_seconds' => $remainingTime],
                429 // Too Many Requests
            );
        }

        // Получаем или создаем работодателя
        $employer = $this->employerService->getOrCreateEmployer($phone);

        if (!$employer) {
            return $this->sendError(
                $this->t('app', 'Failed to process employer registration'),
                null,
                500
            );
        }

        // Определяем, является ли это новой регистрацией
        $isNewRegistration = $this->employerService->isMinimalProfile($employer);

        // Генерируем код валидации
        $code = $this->smsService->generateValidationCode(self::SMS_CODE_LENGTH);

        // Сохраняем код в кеше
        $codeKey = 'sms_code_employer_' . md5($phone);
        \Yii::$app->cache->set($codeKey, $code, self::SMS_CODE_LIFETIME);

        // Отправляем SMS
        $smsResult = $this->smsService->sendValidationCode($phone, $code);

        if (!$smsResult['success']) {
            \Yii::error('Failed to send SMS to employer: ' . $phone . '. Error: ' . $smsResult['message'], __METHOD__);
            return $this->sendError(
                $this->t('app', 'Failed to send SMS code'),
                ['error_details' => $smsResult['message']],
                500
            );
        }

        // Сохраняем время последней отправки
        \Yii::$app->cache->set($lastSentKey, time(), self::SMS_CODE_COOLDOWN);

        // Логируем действие
        $this->loggingService->logAction($employer->id, 'sms_code_sent', [
            'phone' => $phone,
            'is_new_registration' => $isNewRegistration,
            'code_length' => self::SMS_CODE_LENGTH
        ]);

        return $this->sendSuccess([
            'phone' => $phone,
            'is_new_registration' => $isNewRegistration,
            'code_expires_in' => self::SMS_CODE_LIFETIME,
            'can_resend_in' => self::SMS_CODE_COOLDOWN
        ], $this->t('app', 'Validation code sent successfully'));
    }

    /**
     * Подтверждение кода валидации
     *
     * POST /employer/auth/verify-code
     *
     * @return Response
     */
    public function actionVerifyCode()
    {
        $phone = $this->getRequestData('phone');
        $code = $this->getRequestData('code');

        // Валидация входных данных
        if (empty($phone) || empty($code)) {
            return $this->sendValidationError([
                'phone' => empty($phone) ? [$this->t('app', 'Phone number is required')] : [],
                'code' => empty($code) ? [$this->t('app', 'Validation code is required')] : []
            ]);
        }

        // Нормализация номера телефона
        $phone = $this->normalizePhone($phone);

        // Проверяем, является ли это тестовым номером
        $testPhones = ['+998903333333', '+998904444444'];
        $isTestPhone = in_array($phone, $testPhones);

        // Проверяем код из кеша или используем тестовый код для тестовых номеров
        $cacheKey = 'sms_code_employer_' . md5($phone);
        $cachedCode = \Yii::$app->cache->get($cacheKey);

        // Для тестовых номеров принимаем код 1234, для остальных проверяем кеш
        $isValidCode = false;
        if ($isTestPhone && $code === '1234') {
            $isValidCode = true;
        } elseif ($cachedCode && $cachedCode === $code) {
            $isValidCode = true;
        }

        if (!$isValidCode) {
            // Логируем неудачную попытку
            $this->loggingService->logAction(null, 'sms_verification_failed', [
                'phone' => $phone,
                'provided_code' => $code,
                'is_test_phone' => $isTestPhone,
                'reason' => (!$cachedCode && !$isTestPhone) ? 'code_expired' : 'code_mismatch'
            ]);

            return $this->sendError(
                $this->t('app', 'Invalid or expired validation code'),
                ['code' => 'INVALID_CODE'],
                400
            );
        }

        // Находим работодателя
        $employer = $this->employerService->getOrCreateEmployer($phone);

        if (!$employer) {
            return $this->sendError(
                $this->t('app', 'Employer not found'),
                null,
                404
            );
        }

        // Удаляем код из кеша (только для не тестовых номеров)
        if (!$isTestPhone) {
            \Yii::$app->cache->delete($cacheKey);
        }

        // Генерируем токен авторизации
        $authToken = $this->employerService->generateAuthToken($employer->id, self::AUTH_TOKEN_LIFETIME_HOURS);

        if (!$authToken) {
            return $this->sendError(
                $this->t('app', 'Failed to generate authorization token'),
                null,
                500
            );
        }

        // Определяем статус профиля
        $isNewRegistration = $this->employerService->isMinimalProfile($employer);
        $isProfileComplete = $employer->isProfileComplete();

        // Логируем успешную авторизацию
        $this->loggingService->logAction($employer->id, 'sms_verification_success', [
            'phone' => $phone,
            'is_new_registration' => $isNewRegistration,
            'profile_complete' => $isProfileComplete,
            'is_test_phone' => $isTestPhone,
            'auth_method' => $isTestPhone ? 'test_code' : 'sms_code'
        ]);

        return $this->sendSuccess([
            'auth_token' => $authToken,
            'employer' => [
                'id' => $employer->id,
                'phone' => $employer->phone,
                'name' => $employer->name,
                'business_name' => $employer->business_name,
                'business_inn' => $employer->business_inn,
                'business_address' => $employer->business_address,
                'language' => $employer->language,
                'status' => $employer->status,
                'is_profile_complete' => $isProfileComplete,
                'created_at' => $employer->created_at
            ],
            'token_expires_at' => $employer->token_expires_at,
            'is_new_registration' => $isNewRegistration,
            'next_step' => $isNewRegistration ? 'complete_profile' : 'dashboard'
        ], $this->t('app', 'Authorization successful'));
    }

    /**
     * Завершение регистрации работодателя
     *
     * POST /employer/auth/complete-registration
     *
     * @return Response
     */
    public function actionCompleteRegistration()
    {
        $employer = $this->requireAuth();

        $profileData = $this->getRequestData();

        // Валидация обязательных полей
        $requiredFields = ['name', 'business_name'];
        $errors = [];

        foreach ($requiredFields as $field) {
            if (empty($profileData[$field])) {
                $errors[$field] = [$this->t('app', ucfirst($field) . ' is required')];
            }
        }

        if (!empty($errors)) {
            return $this->sendValidationError($errors);
        }

        // Валидация ИНН если указан
        if (!empty($profileData['business_inn'])) {
            if (!preg_match('/^\d{9}$/', $profileData['business_inn'])) {
                $errors['business_inn'] = [$this->t('app', 'Business INN must be 9 digits')];
            }
        }

        if (!empty($errors)) {
            return $this->sendValidationError($errors);
        }

        // Завершаем регистрацию
        $success = $this->employerService->completeRegistration($employer->id, $profileData);

        if (!$success) {
            return $this->sendError(
                $this->t('app', 'Failed to complete registration'),
                null,
                500
            );
        }

        // Получаем обновленные данные работодателя
        $updatedEmployer = $this->employerService->getEmployerProfileById($employer->id);

        // Логируем завершение регистрации
        $this->loggingService->logAction($employer->id, 'registration_completed', [
            'profile_data' => array_keys($profileData)
        ]);

        return $this->sendSuccess([
            'employer' => $updatedEmployer,
            'next_step' => 'dashboard'
        ], $this->t('app', 'Registration completed successfully'));
    }

    /**
     * Проверка статуса авторизации
     *
     * GET /employer/auth/status
     *
     * @return Response
     */
    public function actionStatus()
    {
        $employer = $this->getCurrentEmployer();

        if (!$employer) {
            return $this->sendUnauthorized($this->t('app', 'Not authorized'));
        }

        $profile = $this->employerService->getEmployerProfileById($employer->id);

        return $this->sendSuccess([
            'is_authorized' => true,
            'employer' => $profile,
            'token_expires_at' => $employer->token_expires_at
        ]);
    }

    /**
     * Выход из системы
     *
     * POST /employer/auth/logout
     *
     * @return Response
     */
    public function actionLogout()
    {
        $employer = $this->requireAuth();

        // Обнуляем токен
        $employer->auth_token = null;
        $employer->token_expires_at = null;
        $employer->save(false);

        // Логируем выход
        $this->loggingService->logAction($employer->id, 'logout', []);

        return $this->sendSuccess(null, $this->t('app', 'Logged out successfully'));
    }

    /**
     * Получить данные из запроса (поддерживает JSON и form-data)
     * 
     * @param string $key Ключ параметра
     * @param mixed $default Значение по умолчанию
     * @return mixed
     */
    protected function getRequestData($key = null, $default = null)
    {
        $request = \Yii::$app->request;
        
        // Сначала проверяем JSON данные
        $rawInput = file_get_contents('php://input');
        if (!empty($rawInput)) {
            $jsonData = json_decode($rawInput, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($jsonData)) {
                if ($key === null) {
                    return $jsonData;
                }
                if (isset($jsonData[$key])) {
                    return $jsonData[$key];
                }
            }
        }
        
        // Затем проверяем POST данные
        if ($key === null) {
            return $request->post();
        }
        
        $postValue = $request->post($key, null);
        if ($postValue !== null) {
            return $postValue;
        }
        
        // Проверяем GET параметры (для случаев когда POST превращается в GET)
        $getValue = $request->get($key, null);
        if ($getValue !== null) {
            return $getValue;
        }
        
        return $default;
    }

    /**
     * Нормализация номера телефона
     * 
     * @param string $phone
     * @return string
     */
    protected function normalizePhone($phone)
    {
        // Удаляем все символы кроме цифр и +
        $phone = preg_replace('/[^\d+]/', '', $phone);
        
        // Если номер не начинается с +, добавляем +998 (Узбекистан)
        if (strpos($phone, '+') !== 0) {
            $phone = '+998' . $phone;
        }
        
        return $phone;
    }
}
