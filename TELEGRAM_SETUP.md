# Настройка Telegram бота

## ✅ Локальное тестирование

### 1. Запуск локального сервера
```bash
php -S localhost:8000 -t web
```

### 2. Тестирование webhook
```bash
# GET запрос
curl http://localhost:8000/telegram/webhook

# POST запрос с тестовыми данными
curl -X POST http://localhost:8000/telegram/webhook \
  -H "Content-Type: application/json" \
  -d '{"update_id":123456789,"message":{"message_id":1,"from":{"id":987654321,"is_bot":false,"first_name":"Тестер","username":"test_user"},"chat":{"id":987654321,"first_name":"Тестер","username":"test_user","type":"private"},"date":1640995200,"text":"/start"}}'
```

### 3. Проверка логов
```bash
# Windows
type runtime\logs\telegram.log

# Linux
cat runtime/logs/telegram.log
```

## 🚀 Настройка на сервере

### 1. Требования
- ✅ SSL сертификат (обязательно для Telegram webhook)
- ✅ Веб-сервер (Apache/Nginx) 
- ✅ PHP 7.4+
- ✅ PostgreSQL база данных
- ✅ Доступ к интернету

### 2. Загрузка файлов
Загрузите все файлы проекта на сервер в корневую директорию домена.

### 3. Настройка веб-сервера
Убедитесь что документ рут указывает на папку `web/`.

### 4. Права доступа
```bash
chmod -R 755 runtime/
chmod -R 755 web/assets/
```

### 5. Настройка webhook в Telegram

#### Установка webhook:
```bash
curl -X POST "https://api.telegram.org/bot7639174845:AAFXsbI0beO4NOYEDqz-R5LhulPOEq0H7BI/setWebhook" \
     -d "url=https://ваш-домен.com/telegram/webhook"
```

#### Проверка статуса webhook:
```bash
curl "https://api.telegram.org/bot7639174845:AAFXsbI0beO4NOYEDqz-R5LhulPOEq0H7BI/getWebhookInfo"
```

#### Удаление webhook (если нужно):
```bash
curl -X POST "https://api.telegram.org/bot7639174845:AAFXsbI0beO4NOYEDqz-R5LhulPOEq0H7BI/deleteWebhook"
```

### 6. Тестирование на сервере
После настройки webhook отправьте `/start` боту в Telegram.

## 📁 Структура файлов

### Конфигурация маршрутов: `config/routes.php`
- Автоматическое определение контроллеров и действий
- Поддержка всех модулей (telegram, worker, employer)

### Webhook контроллер: `modules/telegram/controllers/WebhookController.php`
- Обработка входящих сообщений
- Логирование всех действий
- Перенаправление на регистрацию

### Логи: `runtime/logs/telegram.log`
- Все входящие webhook запросы
- Отправляемые сообщения
- Ошибки обработки

## 🔧 Диагностика проблем

### Webhook не получает сообщения:
1. Проверьте SSL сертификат
2. Убедитесь что URL доступен извне
3. Проверьте логи веб-сервера
4. Проверьте статус webhook в Telegram

### Бот не отвечает:
1. Проверьте логи в `runtime/logs/telegram.log`
2. Убедитесь что база данных доступна
3. Проверьте права на запись в папку `runtime/`

### Ошибки базы данных:
1. Запустите миграции: `php yii migrate`
2. Проверьте настройки в `config/db.php`
3. Убедитесь что PostgreSQL запущен

## 📞 Поддержка

При возникновении проблем проверьте:
- Логи приложения: `runtime/logs/app.log`
- Логи Telegram: `runtime/logs/telegram.log`
- Логи веб-сервера (Apache/Nginx)

## 🎯 URL маршруты

- `POST /telegram/webhook` - основной webhook
- `GET|POST /telegram/registration/<action>` - регистрация пользователей
- `GET|POST /telegram/profession/<action>` - выбор профессий
- `GET|POST /worker/<action>` - профиль работника
- `GET|POST /employer/<action>` - профиль работодателя 