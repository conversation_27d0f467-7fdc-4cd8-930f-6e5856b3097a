<?php

namespace app\modules\telegram\controllers;

use yii\web\Controller;
use yii\web\Response;
use app\modules\telegram\models\TelegramService;
use app\modules\worker\models\Worker;
use app\modules\worker\models\Profession;
use app\common\models\WorkerLog;

/**
 * Контроллер редактирования данных работника через Telegram
 */
class EditController extends Controller
{
    /**
     * @var array Отключение CSRF
     */
    public $enableCsrfValidation = false;

    /**
     * @var TelegramService Сервис Telegram
     */
    private $telegramService;

    /**
     * {@inheritdoc}
     */
    public function init()
    {
        parent::init();
        $this->telegramService = \Yii::$app->get('telegramService');
    }

    /**
     * Получение переводимого сообщения
     */
    private function getMessage($key, $params = [], $language = null)
    {
        if ($language) {
            \Yii::$app->language = $language;
        }
        return \Yii::t('telegram', $key, $params);
    }

    /**
     * Показать меню редактирования
     */
    public function actionShowEditMenu($chatId, $language = 'ru')
    {
        // Получаем данные работника
        $worker = Worker::findOne(['chat_id' => $chatId]);
        
        if (!$worker) {
            $text = $this->getMessage('edit.worker_not_found', [], $language);
            $this->telegramService->sendMessage($chatId, $text);
            return $this->asJson(['ok' => false]);
        }

        // Устанавливаем язык
        \Yii::$app->language = $language;

        // Показываем меню редактирования
        $text = $this->getMessage('edit.menu', ['name' => $worker->name], $language);
        
        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => $this->getMessage('edit.button.name', [], $language), 'callback_data' => 'edit_name'],
                    ['text' => $this->getMessage('edit.button.phone', [], $language), 'callback_data' => 'edit_phone']
                ],
                [
                    ['text' => $this->getMessage('edit.button.age', [], $language), 'callback_data' => 'edit_age'],
                    ['text' => $this->getMessage('edit.button.location', [], $language), 'callback_data' => 'edit_location']
                ],
                [
                    ['text' => $this->getMessage('edit.button.profession', [], $language), 'callback_data' => 'edit_profession'],
                    ['text' => $this->getMessage('edit.button.experience', [], $language), 'callback_data' => 'edit_experience']
                ],
                [
                    ['text' => $this->getMessage('edit.button.audio', [], $language), 'callback_data' => 'edit_audio']
                ],
                [
                    ['text' => $this->getMessage('edit.button.back', [], $language), 'callback_data' => 'back_to_main']
                ]
            ]
        ];

        $this->telegramService->sendMessage($chatId, $text, $keyboard);

        // Логируем показ меню редактирования
        WorkerLog::logAction($chatId, 'edit_menu_shown', [
            'worker_id' => $worker->id
        ], $worker->id);

        return $this->asJson(['ok' => true]);
    }

    /**
     * Обработка callback'ов редактирования
     */
    public function actionProcessEditCallback()
    {
        // Получаем данные из webhook
        $update = \Yii::$app->params['currentUpdate'] ?? null;
        
        if (!$update || !isset($update['callback_query'])) {
            return $this->asJson(['ok' => false]);
        }

        $callbackQuery = $update['callback_query'];
        $data = $callbackQuery['data'];
        $chatId = $callbackQuery['from']['id'];

        // Получаем работника
        $worker = Worker::findOne(['chat_id' => $chatId]);
        if (!$worker) {
            return $this->asJson(['ok' => false]);
        }

        // Обрабатываем различные типы редактирования
        switch ($data) {
            case 'edit_name':
                return $this->handleEditName($chatId, $worker);
            case 'edit_phone':
                return $this->handleEditPhone($chatId, $worker);
            case 'edit_age':
                return $this->handleEditAge($chatId, $worker);
            case 'edit_location':
                return $this->handleEditLocation($chatId, $worker);
            case 'edit_profession':
                return $this->handleEditProfession($chatId, $worker);
            case 'edit_experience':
                return $this->handleEditExperience($chatId, $worker);
            case 'edit_audio':
                return $this->handleEditAudio($chatId, $worker);
            case 'back_to_main':
                return $this->handleBackToMain($chatId, $worker);
            default:
                return $this->asJson(['ok' => true]);
        }
    }

    /**
     * Редактирование имени
     */
    private function handleEditName($chatId, $worker)
    {
        // Устанавливаем состояние редактирования имени
        $registration = \app\modules\telegram\models\TemporaryRegistration::findByChatId($chatId);
        if (!$registration) {
            $registration = \app\modules\telegram\models\TemporaryRegistration::createOrUpdate($chatId);
        }
        $registration->status = \app\modules\telegram\models\TemporaryRegistration::STEP_EDIT_NAME;
        $registration->save();

        $text = $this->getMessage('edit.name.prompt', ['current_name' => $worker->name], $worker->language);
        $this->telegramService->sendMessage($chatId, $text);
        
        return $this->asJson(['ok' => true]);
    }

    /**
     * Редактирование телефона
     */
    private function handleEditPhone($chatId, $worker)
    {
        // Устанавливаем состояние редактирования телефона
        $registration = \app\modules\telegram\models\TemporaryRegistration::findByChatId($chatId);
        if (!$registration) {
            $registration = \app\modules\telegram\models\TemporaryRegistration::createOrUpdate($chatId);
        }
        $registration->status = \app\modules\telegram\models\TemporaryRegistration::STEP_EDIT_PHONE;
        $registration->save();

        $sharePhoneButtonText = $this->getMessage('webapp.share_phone', [], $worker->language);
        $keyboard = [
            'keyboard' => [
                [
                    ['text' => $sharePhoneButtonText, 'request_contact' => true]
                ]
            ],
            'resize_keyboard' => true,
            'one_time_keyboard' => true
        ];

        $text = $this->getMessage('edit.phone.prompt', ['current_phone' => $worker->phone], $worker->language);
        $this->telegramService->sendMessage($chatId, $text, $keyboard);

        return $this->asJson(['ok' => true]);
    }

    /**
     * Редактирование возраста
     */
    private function handleEditAge($chatId, $worker)
    {
        // Устанавливаем состояние редактирования возраста
        $registration = \app\modules\telegram\models\TemporaryRegistration::findByChatId($chatId);
        if (!$registration) {
            $registration = \app\modules\telegram\models\TemporaryRegistration::createOrUpdate($chatId);
        }
        $registration->status = \app\modules\telegram\models\TemporaryRegistration::STEP_EDIT_AGE;
        $registration->save();

        $text = $this->getMessage('edit.age.prompt', ['current_age' => $worker->age], $worker->language);
        $this->telegramService->sendMessage($chatId, $text);

        return $this->asJson(['ok' => true]);
    }

    /**
     * Редактирование локации
     */
    private function handleEditLocation($chatId, $worker)
    {
        // Устанавливаем состояние редактирования локации
        $registration = \app\modules\telegram\models\TemporaryRegistration::findByChatId($chatId);
        if (!$registration) {
            $registration = \app\modules\telegram\models\TemporaryRegistration::createOrUpdate($chatId);
        }
        $registration->status = \app\modules\telegram\models\TemporaryRegistration::STEP_EDIT_LOCATION;
        $registration->save();

        $locationButtonText = $this->getMessage('webapp.send_location', [], $worker->language);
        $keyboard = [
            'keyboard' => [
                [
                    ['text' => $locationButtonText, 'request_location' => true]
                ]
            ],
            'resize_keyboard' => true,
            'one_time_keyboard' => true
        ];

        $text = $this->getMessage('edit.location.prompt', [], $worker->language);
        $this->telegramService->sendMessage($chatId, $text, $keyboard);

        return $this->asJson(['ok' => true]);
    }

    /**
     * Редактирование профессии
     */
    private function handleEditProfession($chatId, $worker)
    {
        // Открываем WebView для выбора профессии
        $baseUrl = \Yii::$app->params['webUrl'] ?? \Yii::$app->request->hostInfo;
        $webViewUrl = $baseUrl . '/telegram/profession/select?chat_id=' . $chatId . '&language=' . $worker->language . '&edit=1';
        
        $activityButtonText = $this->getMessage('webapp.select_activity', [], $worker->language);
        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => $activityButtonText, 'web_app' => ['url' => $webViewUrl]]
                ]
            ]
        ];

        $text = $this->getMessage('edit.profession.prompt', [], $worker->language);
        $this->telegramService->sendMessage($chatId, $text, $keyboard);

        return $this->asJson(['ok' => true]);
    }

    /**
     * Редактирование опыта работы
     */
    private function handleEditExperience($chatId, $worker)
    {
        // Устанавливаем состояние редактирования опыта
        $registration = \app\modules\telegram\models\TemporaryRegistration::findByChatId($chatId);
        if (!$registration) {
            $registration = \app\modules\telegram\models\TemporaryRegistration::createOrUpdate($chatId);
        }
        $registration->status = \app\modules\telegram\models\TemporaryRegistration::STEP_EDIT_EXPERIENCE;
        $registration->save();

        $text = $this->getMessage('edit.experience.prompt', ['current_experience' => $worker->experience_years], $worker->language);
        $this->telegramService->sendMessage($chatId, $text);

        return $this->asJson(['ok' => true]);
    }

    /**
     * Редактирование аудио
     */
    private function handleEditAudio($chatId, $worker)
    {
        // Устанавливаем состояние редактирования аудио
        $registration = \app\modules\telegram\models\TemporaryRegistration::findByChatId($chatId);
        if (!$registration) {
            $registration = \app\modules\telegram\models\TemporaryRegistration::createOrUpdate($chatId);
        }
        $registration->status = \app\modules\telegram\models\TemporaryRegistration::STEP_EDIT_AUDIO;
        $registration->save();

        $text = $this->getMessage('edit.audio.prompt', [], $worker->language);
        $this->telegramService->sendMessage($chatId, $text);

        return $this->asJson(['ok' => true]);
    }

    /**
     * Возврат в главное меню
     */
    private function handleBackToMain($chatId, $worker)
    {
        // Показываем сообщение о завершенной регистрации с кнопками управления
        $downloadLinks = "🤖 Android: https://play.google.com/store/apps/details?id=com.ishtop.app\n" .
                        "🍎 iOS: https://apps.apple.com/app/ishtop/id123456789\n";
        
        $text = $this->getMessage('registration.completed', ['downloadLinks' => $downloadLinks], $worker->language);
        
        $editButtonText = $this->getMessage('button.edit_data', [], $worker->language);
        $additionalInfoButtonText = $this->getMessage('button.additional_info', [], $worker->language);
        
        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => $editButtonText, 'callback_data' => 'edit_data'],
                    ['text' => $additionalInfoButtonText, 'callback_data' => 'additional_info']
                ]
            ]
        ];

        $this->telegramService->sendMessage($chatId, $text, $keyboard);

        return $this->asJson(['ok' => true]);
    }
} 