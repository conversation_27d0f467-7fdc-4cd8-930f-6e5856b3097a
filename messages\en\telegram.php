<?php

return [
    // Profession selection
    'profession.select.title' => 'Activity Selection',
    'profession.select.heading' => 'Select Activity',
    'profession.select.description' => 'Choose one or more professions',
    'profession.select.search_placeholder' => '🔍 Search professions...',
    'profession.select.no_results' => 'No professions found',
    'profession.select.submit_button' => 'Select Professions',
    'profession.select.saving' => 'Saving...',
    'profession.select.select_count' => 'Select ({count})',
    'profession.select.selected_profession' => 'Profession selected',
    'profession.select.save_error' => 'Save error',
    'profession.select.connection_error' => 'Connection error',
    
    // Registration flow
    'welcome_message' => "👋 Welcome to Vacant!\n\n<b>Choose your language:</b>",
    'welcome.back' => "👋 Welcome back, {name}!\n\n🎯 Your registration is already completed.",
    'language.selected' => "✅ Language selected: English\n\n👤 <b>What is your name? Enter your name:</b>",
    'name.entered' => "✅ Name: {name}\n\n📱 <b>Share your phone number:</b>",
    'phone.entered' => "✅ Phone: {phone}\n\n📍 <b>Send your geolocation to find jobs nearby:</b>",
    'phone.not_your_own' => "❌ Please share only your own phone number.",
    'phone.use_button' => "📱 Please use the <b>\"📱 Share Phone Number\"</b> button instead of manual input.",
    'location.received' => "✅ Geolocation received\n\n🎂 <b>How old are you? Enter your age:</b>",
    'location.received_with_address' => "✅ Geolocation received\n📍 <b>Your location:</b> {address}\n\n🎂 <b>How old are you? Enter your age:</b>",
    'location.received_short' => "received",
    'location.use_button' => "📍 Please use the <b>\"📍 Send Geolocation\"</b> button instead of text input.",
    'age.invalid' => "❌ Please enter a valid age (16-80 years):",
    'age.entered' => "✅ Age: {age} years\n\n🔧 <b>Now select your activity (profession):</b>",
    'experience.invalid' => "❌ Please enter valid work experience (0-50 years):",
    'experience.entered' => "✅ Work experience: {experience} {years_text}\n\n🎤 <b>Now tell us about yourself with a voice message (up to 60 seconds):</b>",
    'audio.received' => "✅ Audio recording received!\n\n🎯 Registration is almost complete. Choose an action:",
    'audio.failed' => "⚠️ Failed to save audio recording, but it's not mandatory.\n\n🎯 Let's continue with registration:",
    'registration.completed' => "🎉 Congratulations! Registration completed!\n\n📱 Download our app:\n\n{downloadLinks}",
    'additional.info' => "📝 Enter additional information about your work experience:",
    'professions.selected' => "✅ Professions selected: {professionList}\n\n📈 <b>Specify your work experience in years (e.g.: 2):</b>",
    
    // Confirmation message
    'confirm.data' => "📋 <b>Check your information:</b>\n\n👤 Name: {name}\n📱 Phone: {phone}\n🎂 Age: {age} years\n📍 Location: {location}\n🔧 Professions: {professions}\n📈 Experience: {experience} {years_text}\n🎤 Audio: {audio_status}\n\n<b>Is all information correct?</b>",
    'audio.uploaded' => 'uploaded',
    'audio.not_uploaded' => 'not uploaded',
    
    // Years declension
    'years.1' => 'year',
    'years.2_4' => 'years', 
    'years.other' => 'years',
    
    // WebApp buttons
    'webapp.select_activity' => '🔧 Select Activity',
    'webapp.send_location' => '📍 Send Geolocation',
    'webapp.share_phone' => '📱 Share Phone Number',
    
    // Buttons
    'button.confirm_registration' => '✅ Confirm Registration',
    'button.additional_info' => '📝 Fill Additional Info',
    'button.edit_data' => '✏️ Edit Data',
    
    // Edit menu and prompts
    'edit.worker_not_found' => '❌ Worker data not found',
    'edit.menu' => "✏️ <b>Edit data</b>\n\n👤 Name: {name}\n\nWhat would you like to change?",
    'edit.button.name' => '👤 Name',
    'edit.button.phone' => '📱 Phone',
    'edit.button.age' => '🎂 Age',
    'edit.button.location' => '📍 Location',
    'edit.button.profession' => '🔧 Professions',
    'edit.button.experience' => '📈 Experience',
    'edit.button.audio' => '🎤 Audio',
    'edit.button.back' => '⬅️ Back',
    
    // Edit prompts
    'edit.name.prompt' => "👤 <b>Change name</b>\n\nCurrent name: <b>{current_name}</b>\n\nEnter new name:",
    'edit.phone.prompt' => "📱 <b>Change phone</b>\n\nCurrent phone: <b>{current_phone}</b>\n\nShare new phone number:",
    'edit.age.prompt' => "🎂 <b>Change age</b>\n\nCurrent age: <b>{current_age} years</b>\n\nEnter new age (16-80):",
    'edit.location.prompt' => "📍 <b>Change location</b>\n\nSend new geolocation:",
    'edit.profession.prompt' => "🔧 <b>Change professions</b>\n\nSelect new professions:",
    'edit.experience.prompt' => "📈 <b>Change work experience</b>\n\nCurrent experience: <b>{current_experience} years</b>\n\nEnter new work experience (0-50):",
    'edit.audio.prompt' => "🎤 <b>Change audio biography</b>\n\nSend new voice message (up to 60 seconds):",
    
    // Edit success messages
    'edit.name.success' => "✅ <b>Name successfully changed!</b>\n\nNew name: <b>{new_name}</b>",
    'edit.phone.success' => "✅ <b>Phone successfully changed!</b>\n\nNew phone: <b>{new_phone}</b>",
    'edit.age.success' => "✅ <b>Age successfully changed!</b>\n\nNew age: <b>{new_age} years</b>",
    'edit.location.success' => "✅ <b>Location successfully changed!</b>",
    'edit.location.success_with_address' => "✅ <b>Location successfully changed!</b>\n\n📍 New address: <b>{address}</b>",
    'edit.experience.success' => "✅ <b>Work experience successfully changed!</b>\n\nNew experience: <b>{new_experience} {years_text}</b>",
    'edit.audio.success' => "✅ <b>Audio biography successfully updated!</b>",
    'edit.audio.failed' => "⚠️ <b>Failed to update audio</b>, but it's not critical.",
    
    // Audio validation
    'audio.voice_only' => "🎤 <b>Please send a voice message</b>, not music or audio file.\n\nTo record a voice message, press and hold the microphone button in Telegram.",
    
    // Input validation and reminders
    'language.use_button' => "🌐 <b>Please select a language</b> by pressing one of the buttons above.",
    'name.invalid' => "👤 <b>Invalid name!</b>\n\nName should contain only letters, spaces and hyphens (2 to 50 characters).\n\nTry again:",
    'age.numbers_only' => "🎂 <b>Enter numbers only!</b>\n\nAge should be a number from 16 to 80.\n\nTry again:",
    'profession.use_button' => "🔧 <b>Please select professions</b> by pressing the <b>\"🔧 Select Activity\"</b> button above.",
    'experience.numbers_only' => "📈 <b>Enter numbers only!</b>\n\nWork experience should be a number from 0 to 50.\n\nTry again:",
    'audio.use_voice' => "🎤 <b>Please send a voice message</b>\n\nPress and hold the microphone button in Telegram to record.",
    'media.voice_only' => "🎤 <b>Only voice messages are needed at this stage</b>\n\nPlease don't send photos, videos or stickers.",
]; 