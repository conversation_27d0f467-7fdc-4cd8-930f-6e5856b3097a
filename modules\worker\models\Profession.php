<?php

namespace app\modules\worker\models;

use yii\db\ActiveRecord;

/**
 * Модель профессии
 * 
 * @property int $id
 * @property string $name_uz Название на узбекском
 * @property string $name_ru Название на русском
 * @property string $name_en Название на английском
 * @property string $created_at
 * @property string $deleted_at
 */
class Profession extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'professions';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['created_at', 'deleted_at'], 'safe'],
            [['name_uz', 'name_ru', 'name_en'], 'string', 'max' => 100],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name_uz' => 'Название на узбекском',
            'name_ru' => 'Название на русском', 
            'name_en' => 'Название на английском',
            'created_at' => 'Дата создания',
            'deleted_at' => 'Дата удаления',
        ];
    }

    /**
     * Получение названия профессии на указанном языке
     * @param string $language Код языка (uz, ru, en)
     * @return string
     */
    public function getName($language = 'ru')
    {
        switch ($language) {
            case 'uz':
                return $this->name_uz ?: $this->name_ru ?: $this->name_en;
            case 'en':
                return $this->name_en ?: $this->name_ru ?: $this->name_uz;
            case 'ru':
            default:
                return $this->name_ru ?: $this->name_en ?: $this->name_uz;
        }
    }

    /**
     * Получение работников с данной профессией
     */
    public function getWorkers()
    {
        return $this->hasMany(Worker::class, ['id' => 'worker_id'])
            ->viaTable('worker_professions', ['profession_id' => 'id']);
    }
} 