<?php

namespace tests\employer;

use PHPUnit\Framework\TestCase;
use Yii;
use yii\web\Application;
use yii\caching\ArrayCache;
use app\modules\employer\controllers\AuthController;
use app\modules\employer\services\EmployerService;
use app\modules\employer\models\Employer;
use app\common\services\SmsService;
use app\common\services\LoggingService;

/**
 * Тесты для SMS функциональности AuthController
 */
class AuthControllerSmsTest extends TestCase
{
    private $controller;
    private $mockSmsService;
    private $mockEmployerService;
    private $mockLoggingService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Настройка Yii приложения для тестов
        if (!isset(Yii::$app)) {
            new Application([
                'id' => 'test-app',
                'basePath' => dirname(__DIR__, 2),
                'components' => [
                    'cache' => [
                        'class' => ArrayCache::class,
                    ],
                    'i18n' => [
                        'translations' => [
                            'app' => [
                                'class' => 'yii\i18n\PhpMessageSource',
                                'basePath' => '@app/messages',
                            ],
                        ],
                    ],
                ],
            ]);
        }

        // Создаем контроллер
        $this->controller = new AuthController('auth', null);
        
        // Создаем моки сервисов
        $this->mockSmsService = $this->createMock(SmsService::class);
        $this->mockEmployerService = $this->createMock(EmployerService::class);
        $this->mockLoggingService = $this->createMock(LoggingService::class);
        
        // Внедряем моки в контроллер через рефлексию
        $reflection = new \ReflectionClass($this->controller);
        
        $smsProperty = $reflection->getProperty('smsService');
        $smsProperty->setAccessible(true);
        $smsProperty->setValue($this->controller, $this->mockSmsService);
        
        $employerProperty = $reflection->getProperty('employerService');
        $employerProperty->setAccessible(true);
        $employerProperty->setValue($this->controller, $this->mockEmployerService);
        
        $loggingProperty = $reflection->getProperty('loggingService');
        $loggingProperty->setAccessible(true);
        $loggingProperty->setValue($this->controller, $this->mockLoggingService);
    }

    protected function tearDown(): void
    {
        Yii::$app->cache->flush();
        parent::tearDown();
    }

    /**
     * Тест успешной отправки SMS кода
     */
    public function testSendCodeSuccess()
    {
        $phone = '+998901234567';
        $normalizedPhone = '+998901234567';
        $code = '1234';
        
        // Мокаем employer
        $employer = $this->createMock(Employer::class);
        $employer->id = 1;
        
        // Настраиваем моки
        $this->mockEmployerService
            ->expects($this->once())
            ->method('getOrCreateEmployer')
            ->with($normalizedPhone)
            ->willReturn($employer);
            
        $this->mockEmployerService
            ->expects($this->once())
            ->method('isMinimalProfile')
            ->with($employer)
            ->willReturn(true);
            
        $this->mockSmsService
            ->expects($this->once())
            ->method('generateValidationCode')
            ->with(4)
            ->willReturn($code);
            
        $this->mockSmsService
            ->expects($this->once())
            ->method('sendValidationCode')
            ->with($normalizedPhone, $code)
            ->willReturn(['success' => true, 'message' => 'SMS sent']);
            
        $this->mockLoggingService
            ->expects($this->once())
            ->method('logAction')
            ->with(1, 'sms_code_sent', $this->isType('array'));

        // Симулируем POST данные
        $_POST = ['phone' => $phone];
        
        // Вызываем метод
        $result = $this->controller->actionSendCode();
        
        // Проверяем результат
        $this->assertIsArray($result);
        
        // Проверяем, что код сохранен в кеше
        $cacheKey = 'sms_code_employer_' . md5($normalizedPhone);
        $cachedCode = Yii::$app->cache->get($cacheKey);
        $this->assertEquals($code, $cachedCode);
        
        // Проверяем, что время последней отправки сохранено
        $lastSentKey = 'sms_last_sent_employer_' . md5($normalizedPhone);
        $lastSentTime = Yii::$app->cache->get($lastSentKey);
        $this->assertNotNull($lastSentTime);
        $this->assertLessThanOrEqual(time(), $lastSentTime);
    }

    /**
     * Тест отправки SMS с ограничением по времени (cooldown)
     */
    public function testSendCodeWithCooldown()
    {
        $phone = '+998901234567';
        $normalizedPhone = '+998901234567';
        
        // Устанавливаем время последней отправки (30 секунд назад)
        $lastSentKey = 'sms_last_sent_employer_' . md5($normalizedPhone);
        Yii::$app->cache->set($lastSentKey, time() - 30, 120);
        
        // Симулируем POST данные
        $_POST = ['phone' => $phone];
        
        // Вызываем метод
        $result = $this->controller->actionSendCode();
        
        // Проверяем, что возвращается ошибка с кодом 429
        $this->assertIsArray($result);
        // В реальном контроллере это будет Response объект с кодом 429
    }

    /**
     * Тест успешной проверки SMS кода
     */
    public function testVerifyCodeSuccess()
    {
        $phone = '+998901234567';
        $normalizedPhone = '+998901234567';
        $code = '1234';
        $authToken = 'test_auth_token';
        
        // Сохраняем код в кеше
        $cacheKey = 'sms_code_employer_' . md5($normalizedPhone);
        Yii::$app->cache->set($cacheKey, $code, 300);
        
        // Мокаем employer
        $employer = $this->createMock(Employer::class);
        $employer->id = 1;
        $employer->phone = $normalizedPhone;
        $employer->name = 'Test Employer';
        $employer->business_name = 'Test Business';
        $employer->language = 'uz';
        $employer->status = 'active';
        
        $employer->expects($this->once())
            ->method('isProfileComplete')
            ->willReturn(true);
        
        // Настраиваем моки
        $this->mockEmployerService
            ->expects($this->once())
            ->method('getOrCreateEmployer')
            ->with($normalizedPhone)
            ->willReturn($employer);
            
        $this->mockEmployerService
            ->expects($this->once())
            ->method('isMinimalProfile')
            ->with($employer)
            ->willReturn(false);
            
        $this->mockEmployerService
            ->expects($this->once())
            ->method('generateAuthToken')
            ->with(1, 168) // 24 * 7 hours
            ->willReturn($authToken);
            
        $this->mockLoggingService
            ->expects($this->once())
            ->method('logAction')
            ->with(1, 'sms_verification_success', $this->isType('array'));

        // Симулируем POST данные
        $_POST = ['phone' => $phone, 'code' => $code];
        
        // Вызываем метод
        $result = $this->controller->actionVerifyCode();
        
        // Проверяем результат
        $this->assertIsArray($result);
        
        // Проверяем, что код удален из кеша
        $cachedCode = Yii::$app->cache->get($cacheKey);
        $this->assertFalse($cachedCode);
    }

    /**
     * Тест проверки неверного SMS кода
     */
    public function testVerifyCodeInvalid()
    {
        $phone = '+998901234567';
        $normalizedPhone = '+998901234567';
        $correctCode = '1234';
        $wrongCode = '5678';
        
        // Сохраняем правильный код в кеше
        $cacheKey = 'sms_code_employer_' . md5($normalizedPhone);
        Yii::$app->cache->set($cacheKey, $correctCode, 300);
        
        $this->mockLoggingService
            ->expects($this->once())
            ->method('logAction')
            ->with(null, 'sms_verification_failed', $this->isType('array'));

        // Симулируем POST данные с неверным кодом
        $_POST = ['phone' => $phone, 'code' => $wrongCode];
        
        // Вызываем метод
        $result = $this->controller->actionVerifyCode();
        
        // Проверяем результат - должна быть ошибка
        $this->assertIsArray($result);
        
        // Проверяем, что код остался в кеше (не удален)
        $cachedCode = Yii::$app->cache->get($cacheKey);
        $this->assertEquals($correctCode, $cachedCode);
    }

    /**
     * Тест проверки тестового номера с фиксированным кодом
     */
    public function testVerifyTestPhoneWithFixedCode()
    {
        $testPhone = '+998903333333';
        $testCode = '1234';
        $authToken = 'test_auth_token';
        
        // Мокаем employer
        $employer = $this->createMock(Employer::class);
        $employer->id = 1;
        $employer->phone = $testPhone;
        
        $employer->expects($this->once())
            ->method('isProfileComplete')
            ->willReturn(false);
        
        // Настраиваем моки
        $this->mockEmployerService
            ->expects($this->once())
            ->method('getOrCreateEmployer')
            ->with($testPhone)
            ->willReturn($employer);
            
        $this->mockEmployerService
            ->expects($this->once())
            ->method('isMinimalProfile')
            ->with($employer)
            ->willReturn(true);
            
        $this->mockEmployerService
            ->expects($this->once())
            ->method('generateAuthToken')
            ->with(1, 168)
            ->willReturn($authToken);

        // Симулируем POST данные
        $_POST = ['phone' => $testPhone, 'code' => $testCode];
        
        // Вызываем метод
        $result = $this->controller->actionVerifyCode();
        
        // Проверяем результат
        $this->assertIsArray($result);
    }

    /**
     * Тест отправки SMS с ошибкой сервиса
     */
    public function testSendCodeSmsServiceError()
    {
        $phone = '+998901234567';
        $normalizedPhone = '+998901234567';
        $code = '1234';
        
        // Мокаем employer
        $employer = $this->createMock(Employer::class);
        $employer->id = 1;
        
        // Настраиваем моки
        $this->mockEmployerService
            ->expects($this->once())
            ->method('getOrCreateEmployer')
            ->with($normalizedPhone)
            ->willReturn($employer);
            
        $this->mockEmployerService
            ->expects($this->once())
            ->method('isMinimalProfile')
            ->with($employer)
            ->willReturn(true);
            
        $this->mockSmsService
            ->expects($this->once())
            ->method('generateValidationCode')
            ->with(4)
            ->willReturn($code);
            
        // SMS сервис возвращает ошибку
        $this->mockSmsService
            ->expects($this->once())
            ->method('sendValidationCode')
            ->with($normalizedPhone, $code)
            ->willReturn(['success' => false, 'message' => 'SMS service error']);

        // Симулируем POST данные
        $_POST = ['phone' => $phone];
        
        // Вызываем метод
        $result = $this->controller->actionSendCode();
        
        // Проверяем результат - должна быть ошибка
        $this->assertIsArray($result);
        
        // Проверяем, что время последней отправки НЕ сохранено
        $lastSentKey = 'sms_last_sent_employer_' . md5($normalizedPhone);
        $lastSentTime = Yii::$app->cache->get($lastSentKey);
        $this->assertFalse($lastSentTime);
    }
}
