<?php

return [
    // Общие сообщения
    'Phone number is required' => 'Номер телефона обязателен',
    'Worker with this phone number not found' => 'Работник с таким номером телефона не найден',
    'Worker not found' => 'Работник не найден',
    'Successfully authenticated' => 'Успешная авторизация',
    'Token not provided' => 'Токен не предоставлен',
    'Successfully logged out' => 'Успешный выход из системы',
    'Invalid or expired token' => 'Недействительный или истекший токен',
    'Token is valid' => 'Токен действителен',
    'Token refreshed successfully' => 'Токен успешно обновлен',
    'Authentication required' => 'Требуется авторизация',
    
    // Вакансии
    'Vacancies retrieved successfully' => 'Вакансии успешно получены',
    'Error retrieving vacancies' => 'Ошибка при получении вакансий',
    'Search completed successfully' => 'Поиск успешно завершен',
    'Error searching vacancies' => 'Ошибка при поиске вакансий',
    'Invalid vacancy ID' => 'Неверный ID вакансии',
    'Vacancy not found' => 'Вакансия не найдена',
    'Vacancy details retrieved successfully' => 'Детали вакансии успешно получены',
    'Error retrieving vacancy details' => 'Ошибка при получении деталей вакансии',
    
    // Профиль
    'Profile not found' => 'Профиль не найден',
    'Profile retrieved successfully' => 'Профиль успешно получен',
    'Error retrieving profile' => 'Ошибка при получении профиля',
    'Failed to update profile' => 'Не удалось обновить профиль',
    'Profile updated successfully' => 'Профиль успешно обновлен',
    'Error updating profile' => 'Ошибка при обновлении профиля',
    
    // Избранные вакансии
    'Valid vacancy ID is required' => 'Требуется корректный ID вакансии',
    'Failed to add vacancy to favorites' => 'Не удалось добавить вакансию в избранное',
    'Vacancy added to favorites successfully' => 'Вакансия успешно добавлена в избранное',
    'Error adding to favorites' => 'Ошибка при добавлении в избранное',
    'Vacancy not found in favorites' => 'Вакансия не найдена в избранном',
    'Vacancy removed from favorites successfully' => 'Вакансия успешно удалена из избранного',
    'Error removing from favorites' => 'Ошибка при удалении из избранного',
    'Favorite vacancies retrieved successfully' => 'Избранные вакансии успешно получены',
    'Error retrieving favorite vacancies' => 'Ошибка при получении избранных вакансий',
    
    // Валидация профиля
    'Name cannot be empty' => 'Имя не может быть пустым',
    'Name is too long' => 'Имя слишком длинное',
    'Age must be between 16 and 80' => 'Возраст должен быть от 16 до 80 лет',
    'Experience must be between 0 and 50 years' => 'Опыт работы должен быть от 0 до 50 лет',
    'Invalid language' => 'Неверный язык',
    'Both latitude and longitude are required' => 'Требуются и широта, и долгота',
    'Invalid latitude' => 'Неверная широта',
    'Invalid longitude' => 'Неверная долгота',
    
    // Аудио
    'Audio file is required' => 'Требуется аудиофайл',
    'Failed to upload audio file' => 'Не удалось загрузить аудиофайл',
    'Invalid file type' => 'Неверный тип файла',
    'File too large' => 'Файл слишком большой',
    'Audio file uploaded successfully' => 'Аудиофайл успешно загружен',
    'Error uploading audio file' => 'Ошибка при загрузке аудиофайла',
    
    // Общие ошибки
    'Validation failed' => 'Ошибка валидации',
    'Resource not found' => 'Ресурс не найден',
    'Unauthorized' => 'Не авторизован',
    'Forbidden' => 'Запрещено',


    // Worker
        'worker.name' => 'Имя',
        'worker.age' => 'Возраст',
        'worker.experience_years' => 'Опыт работы (лет)',
        'worker.about' => 'О себе',
        'worker.language' => 'Язык',
        'worker.lat' => 'Широта',
        'worker.lng' => 'Долгота',
        'worker.profession_ids' => 'Профессии',
        'worker.profession_not_found' => 'Профессия не найдена',
        'worker.audio_file' => 'Аудиофайл',
        'worker.audio_file_too_large' => 'Размер файла не должен превышать 10MB',
        'worker.audio_file_wrong_extension' => 'Разрешены только аудиофайлы: mp3, wav, ogg, m4a, aac',
        'worker.audio_file_wrong_mime_type' => 'Неверный тип файла. Загружайте только аудиофайлы',
        'worker.vacancy_id' => 'ID вакансии',
        'worker.vacancy_not_found' => 'Вакансия не найдена или неактивна',
];
