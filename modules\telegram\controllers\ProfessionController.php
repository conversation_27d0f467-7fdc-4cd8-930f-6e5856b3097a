<?php

namespace app\modules\telegram\controllers;

use yii\web\Controller;
use yii\web\Response;
use app\modules\worker\models\Profession;
use app\modules\telegram\models\TemporaryRegistration;

/**
 * Контроллер выбора профессий через WebView
 */
class ProfessionController extends Controller
{
    public $layout = false;
    
    /**
     * @var array Отключение CSRF для WebView
     */
    public $enableCsrfValidation = false;

    /**
     * Страница выбора профессий
     */
    public function actionSelect($chat_id, $language = null)
    {
        // Получаем язык из URL параметра или из регистрации
        if (!$language) {
            $registration = TemporaryRegistration::findByChatId($chat_id);
            $language = $registration ? $registration->language : 'ru';
        }
        
        // Устанавливаем язык для Yii::t()
        \Yii::$app->language = $language;
        
        $professions = Profession::find()
            ->where(['deleted_at' => null])
            ->orderBy("name_{$language} ASC")
            ->all();

        return $this->render('select', [
            'professions' => $professions,
            'chat_id' => $chat_id,
            'language' => $language
        ]);
    }

    /**
     * Сохранение выбранной профессии
     */
    public function actionSave()
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;

        $chatId = \Yii::$app->request->post('chat_id');
        $professionIds = \Yii::$app->request->post('profession_ids', []);

        if (!$chatId || empty($professionIds)) {
            return ['success' => false, 'message' => 'Не выбрана профессия'];
        }

        $registration = TemporaryRegistration::findByChatId($chatId);
        if (!$registration) {
            return ['success' => false, 'message' => 'Регистрация не найдена'];
        }

        // Получаем Worker по chat_id
        $worker = \app\modules\worker\models\Worker::findOne(['chat_id' => $chatId]);
        if (!$worker) {
            return ['success' => false, 'message' => 'Работник не найден'];
        }

        // Сохраняем выбранные профессии
        $selectedProfessions = [];
        foreach ($professionIds as $professionId) {
            $profession = Profession::findOne($professionId);
            if ($profession) {
                // Проверяем, нет ли уже такой связи
                $exists = \Yii::$app->db->createCommand(
                    'SELECT COUNT(*) FROM worker_professions WHERE worker_id = :worker_id AND profession_id = :profession_id'
                )->bindValues([
                    ':worker_id' => $worker->id,
                    ':profession_id' => $profession->id
                ])->queryScalar();

                if ($exists == 0) {
                    // Создаем связь worker-profession
                    \Yii::$app->db->createCommand()
                        ->insert('worker_professions', [
                            'worker_id' => $worker->id,
                            'profession_id' => $profession->id
                        ])
                        ->execute();
                }

                // Получаем название профессии на языке пользователя
                $selectedProfessions[] = $profession->getName($registration->language);
            }
        }
        
        if (!empty($selectedProfessions)) {
            // Переходим к этапу ввода опыта работы
            $registration->status = TemporaryRegistration::STEP_EXPERIENCE;
            $registration->save();

            // Отправляем сообщение в Telegram о том что профессии выбраны
            $this->sendProfessionSelectedMessage($chatId, $selectedProfessions, $registration->language);

            return ['success' => true, 'profession' => implode(', ', $selectedProfessions)];
        }

        return ['success' => false, 'message' => 'Профессия не найдена'];
    }

    /**
     * Отправка сообщения в Telegram о выборе профессий
     */
    private function sendProfessionSelectedMessage($chatId, $selectedProfessions, $language = 'ru')
    {
        try {
            $telegramService = \Yii::$app->telegramService;
            if (!$telegramService) {
                \Yii::error("TelegramService not available", 'telegram');
                return false;
            }

            $professionList = implode(', ', $selectedProfessions);
            
            // Устанавливаем язык для переводов
            \Yii::$app->language = $language;
            
            $text = \Yii::t('telegram', 'professions.selected', ['professionList' => $professionList]);
            
            return $telegramService->sendMessage($chatId, $text);
            
        } catch (\Exception $e) {
            \Yii::error("Failed to send profession selected message: " . $e->getMessage(), 'telegram');
            return false;
        }
    }
} 