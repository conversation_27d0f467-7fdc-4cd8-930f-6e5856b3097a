<?php

namespace app\modules\worker\models;

use yii\base\Model;

/**
 * Форм-модель для обновления профиля работника
 */
class ProfileUpdateForm extends Model
{
    public $name;
    public $age;
    public $experience_years;
    public $about;
    public $language;
    public $lat;
    public $lng;
    public $profession_ids;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name', 'about', 'language'], 'string'],
            [['name'], 'string', 'max' => 100],
            [['name'], 'trim'],
            [['about'], 'string', 'max' => 1000],
            
            [['age', 'experience_years'], 'integer'],
            [['age'], 'integer', 'min' => 16, 'max' => 80],
            [['experience_years'], 'integer', 'min' => 0, 'max' => 50],
            
            [['lat', 'lng'], 'number'],
            [['lat'], 'number', 'min' => -90, 'max' => 90],
            [['lng'], 'number', 'min' => -180, 'max' => 180],
            
            [['language'], 'in', 'range' => ['ru', 'uz', 'en']],
            
            [['profession_ids'], 'each', 'rule' => ['integer', 'min' => 1]],
            [['profession_ids'], 'validateProfessions'],
            
            // Валидация координат - если указана одна, то нужна и вторая
            [['lng'], 'required', 'when' => function($model) {
                return !empty($model->lat);
            }, 'whenClient' => "function (attribute, value) { return $('#profileupdateform-lat').val() != ''; }"],
            [['lat'], 'required', 'when' => function($model) {
                return !empty($model->lng);
            }, 'whenClient' => "function (attribute, value) { return $('#profileupdateform-lng').val() != ''; }"],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'name' => \Yii::t('app', 'worker.name'),
            'age' => \Yii::t('app', 'worker.age'),
            'experience_years' => \Yii::t('app', 'worker.experience_years'),
            'about' => \Yii::t('app', 'worker.about'),
            'language' => \Yii::t('app', 'worker.language'),
            'lat' => \Yii::t('app', 'worker.lat'),
            'lng' => \Yii::t('app', 'worker.lng'),
            'profession_ids' => \Yii::t('app', 'worker.profession_ids'),
        ];
    }

    /**
     * Валидация профессий
     */
    public function validateProfessions($attribute, $params)
    {
        if (!empty($this->profession_ids)) {
            foreach ($this->profession_ids as $professionId) {
                $profession = Profession::findOne([
                    'id' => $professionId,
                    'deleted_at' => null
                ]);
                
                if (!$profession) {
                    $this->addError($attribute, \Yii::t('app', 'worker.profession_not_found', ['profession_id' => $professionId]));
                    break;
                }
            }
        }
    }

    /**
     * Загрузить данные из POST запроса
     */
    public function loadFromRequest($request)
    {
        $data = [];
        
        if ($request->post('name') !== null) {
            $data['name'] = $request->post('name');
        }
        
        if ($request->post('age') !== null) {
            $data['age'] = $request->post('age');
        }
        
        if ($request->post('experience_years') !== null) {
            $data['experience_years'] = $request->post('experience_years');
        }
        
        if ($request->post('about') !== null) {
            $data['about'] = $request->post('about');
        }
        
        if ($request->post('language') !== null) {
            $data['language'] = $request->post('language');
        }
        
        if ($request->post('lat') !== null) {
            $data['lat'] = $request->post('lat');
        }
        
        if ($request->post('lng') !== null) {
            $data['lng'] = $request->post('lng');
        }
        
        if ($request->post('profession_ids') !== null) {
            $professionIds = $request->post('profession_ids');
            if (is_string($professionIds)) {
                $professionIds = explode(',', $professionIds);
            }
            $data['profession_ids'] = array_map('intval', array_filter($professionIds));
        }
        
        return $this->load($data, '');
    }

    /**
     * Получить данные для обновления профиля
     */
    public function getUpdateData()
    {
        $data = [];
        
        foreach (['name', 'age', 'experience_years', 'about', 'language', 'lat', 'lng', 'profession_ids'] as $attribute) {
            if ($this->$attribute !== null) {
                $data[$attribute] = $this->$attribute;
            }
        }
        
        return $data;
    }
} 