{"id": "worker-api-local-environment", "name": "Worker API Local", "values": [{"key": "base_url", "value": "http://localhost/ish_top", "description": "Базовый URL для Worker API", "enabled": true}, {"key": "test_phone_full", "value": "+998901111111", "description": "Тестовый номер с полным профилем", "enabled": true}, {"key": "test_phone_minimal", "value": "+998902222222", "description": "Тестовый номер с минимальным профилем", "enabled": true}, {"key": "access_token", "value": "", "description": "Токен доступа (заполняется автоматически)", "enabled": true}, {"key": "worker_id", "value": "", "description": "ID работника (заполняется автоматически)", "enabled": true}, {"key": "test_vacancy_id", "value": "1", "description": "ID тестовой вакансии", "enabled": true}], "_postman_variable_scope": "environment"}