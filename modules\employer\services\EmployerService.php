<?php

namespace app\modules\employer\services;

use app\modules\employer\models\Employer;
use yii\db\Exception;

/**
 * Сервис для работы с профилем работодателя
 */
class EmployerService
{
    /**
     * Получить или создать работодателя по номеру телефона
     * 
     * @param string $phone
     * @return Employer|null
     */
    public function getOrCreateEmployer($phone)
    {
        $employer = Employer::find()
            ->where(['phone' => $phone, 'deleted_at' => null])
            ->one();

        if (!$employer) {
            $employer = new Employer();
            $employer->phone = $phone;
            $employer->status = Employer::STATUS_PENDING;
            $employer->language = Employer::LANGUAGE_UZ;
            
            if (!$employer->save()) {
                \Yii::error('Failed to create employer: ' . json_encode($employer->errors), __METHOD__);
                return null;
            }
        }

        return $employer;
    }

    /**
     * Получить профиль работодателя по ID
     * 
     * @param int $id
     * @return array|null
     */
    public function getEmployerProfileById($id)
    {
        $employer = Employer::find()
            ->where(['id' => $id, 'deleted_at' => null])
            ->one();

        if (!$employer) {
            return null;
        }

        return $this->formatEmployerProfile($employer);
    }

    /**
     * Получить профиль работодателя по токену
     * 
     * @param string $token
     * @return array|null
     */
    public function getEmployerProfileByToken($token)
    {
        $employer = Employer::findByAuthToken($token);

        if (!$employer) {
            return null;
        }

        return $this->formatEmployerProfile($employer);
    }

    /**
     * Обновить профиль работодателя
     * 
     * @param int $employerId
     * @param array $data
     * @return bool
     */
    public function updateEmployerProfile($employerId, $data)
    {
        $employer = Employer::findOne(['id' => $employerId, 'deleted_at' => null]);
        
        if (!$employer) {
            return false;
        }

        $allowedFields = ['name', 'business_name', 'business_inn', 'business_address', 'language'];
        $hasChanges = false;
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $oldValue = $employer->$field;
                $newValue = $data[$field];
                
                if ($oldValue !== $newValue) {
                    $employer->$field = $newValue;
                    $hasChanges = true;
                }
            }
        }

        // Если профиль завершен, обновляем статус
        if ($employer->isProfileComplete() && $employer->status == Employer::STATUS_PENDING) {
            $employer->status = Employer::STATUS_ACTIVE;
            $hasChanges = true;
        }

        if (!$hasChanges) {
            return true; // Если нет изменений, считаем это успехом
        }

        return $employer->save();
    }

    /**
     * Генерация токена авторизации
     * 
     * @param int $employerId
     * @param int $lifetimeHours
     * @return string|null
     */
    public function generateAuthToken($employerId, $lifetimeHours = 168)
    {
        $employer = Employer::findOne(['id' => $employerId, 'deleted_at' => null]);
        
        if (!$employer) {
            return null;
        }

        $token = $employer->generateAuthToken($lifetimeHours);
        
        if ($employer->save()) {
            return $token;
        }

        return null;
    }

    /**
     * Валидация токена авторизации
     * 
     * @param string $token
     * @return Employer|null
     */
    public function validateAuthToken($token)
    {
        return Employer::findByAuthToken($token);
    }

    /**
     * Смена языка работодателя
     * 
     * @param int $employerId
     * @param string $language
     * @return bool
     */
    public function changeLanguage($employerId, $language)
    {
        $employer = Employer::findOne(['id' => $employerId, 'deleted_at' => null]);
        
        if (!$employer) {
            return false;
        }

        $allowedLanguages = [Employer::LANGUAGE_UZ, Employer::LANGUAGE_RU, Employer::LANGUAGE_EN];
        
        if (!in_array($language, $allowedLanguages)) {
            return false;
        }

        $employer->language = $language;
        return $employer->save();
    }

    /**
     * Завершение регистрации работодателя
     * 
     * @param int $employerId
     * @param array $profileData
     * @return bool
     */
    public function completeRegistration($employerId, $profileData)
    {
        $employer = Employer::findOne(['id' => $employerId, 'deleted_at' => null]);
        
        if (!$employer) {
            return false;
        }

        $requiredFields = ['name', 'business_name'];
        
        foreach ($requiredFields as $field) {
            if (empty($profileData[$field])) {
                return false;
            }
            $employer->$field = $profileData[$field];
        }

        // Опциональные поля
        $optionalFields = ['business_inn', 'business_address'];
        foreach ($optionalFields as $field) {
            if (isset($profileData[$field])) {
                $employer->$field = $profileData[$field];
            }
        }

        $employer->status = Employer::STATUS_ACTIVE;
        
        return $employer->save();
    }

    /**
     * Проверка является ли профиль минимальным (только телефон)
     * 
     * @param Employer $employer
     * @return bool
     */
    public function isMinimalProfile($employer)
    {
        return empty($employer->name) && empty($employer->business_name);
    }

    /**
     * Форматирование профиля работодателя для API
     * 
     * @param Employer $employer
     * @return array
     */
    private function formatEmployerProfile($employer)
    {
        return [
            'id' => $employer->id,
            'name' => $employer->name,
            'phone' => $employer->phone,
            'business_name' => $employer->business_name,
            'business_inn' => $employer->business_inn,
            'business_address' => $employer->business_address,
            'status' => $employer->status,
            'language' => $employer->language,
            'is_profile_complete' => $employer->isProfileComplete(),
            'created_at' => $employer->created_at,
        ];
    }

    /**
     * Получить количество вакансий работодателя
     * 
     * @param int $employerId
     * @return int
     */
    public function getVacanciesCount($employerId)
    {
        return \app\modules\employer\models\Vacancy::find()
            ->where(['employer_id' => $employerId, 'deleted_at' => null])
            ->count();
    }

    /**
     * Получить количество избранных работников
     * 
     * @param int $employerId
     * @return int
     */
    public function getFavoritesCount($employerId)
    {
        return (new \yii\db\Query())
            ->from('{{%employer_favorites}}')
            ->where(['employer_id' => $employerId, 'deleted_at' => null])
            ->count();
    }
}
