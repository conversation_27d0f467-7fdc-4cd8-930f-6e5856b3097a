# Быстрое руководство по тестированию Worker API в Postman

## Что я создал для вас:

1. **POSTMAN_WORKER_API_TESTING.md** - Подробное руководство по тестированию
2. **Worker_API_Postman_Collection.json** - Готовая коллекция запросов для Postman
3. **Worker_API_Environment.postman_environment.json** - Файл окружения с переменными

## Быстрая установка

### 1. Импорт в Postman

1. Откройте Postman
2. Нажмите **Import** (левый верхний угол)
3. Перетащите файл `Worker_API_Postman_Collection.json` в окно импорта
4. Нажмите **Import** для коллекции
5. Повторите для файла `Worker_API_Environment.postman_environment.json`

### 2. Настройка окружения

1. В правом верхнем углу выберите окружение "Worker API Local"
2. Нажмите на иконку "глаз" рядом с названием окружения
3. Проверьте/измените переменные:
   - `base_url`: установите ваш локальный URL (например: `http://localhost/ish_top`)
   - `test_phone`: установите тестовый номер телефона

### 3. Последовательность тестирования

**Обязательно выполняйте в этом порядке:**

1. **Authentication** → "1. Send SMS Code"
2. **Authentication** → "2. Login with Code" (используйте код `1234` для тестирования)
3. **Profile** → "1. View Profile"
4. **Profile** → "2. Update Profile"
5. **Vacancies** → "1. Get Professions List"
6. **Vacancies** → "2. Get Vacancies List"

## Основные endpoints для тестирования

### 🔐 Аутентификация
- `POST /worker/auth/send-code` - Отправка SMS кода
- `POST /worker/auth/login` - Вход в систему
- `GET /worker/auth/verify` - Проверка токена
- `POST /worker/auth/logout` - Выход

### 👤 Профиль  
- `GET /worker/profile/index` - Просмотр профиля
- `PUT /worker/profile/update` - Обновление профиля
- `POST /worker/profile/upload-audio` - Загрузка аудио

### 💼 Вакансии
- `GET /worker/vacancy/professions` - Список профессий (публичный)
- `GET /worker/vacancy/list` - Список вакансий (требует авторизации)
- `GET /worker/vacancy/search` - Поиск вакансий
- `GET /worker/vacancy/detail/{id}` - Детали вакансии

### ⭐ Избранное
- `POST /worker/profile/add-favorite` - Добавить в избранное
- `GET /worker/profile/favorites` - Список избранных
- `POST /worker/profile/remove-favorite` - Удалить из избранного

## Полезные советы

### Автоматические тесты
Каждый запрос в коллекции содержит автоматические тесты, которые:
- Проверяют код ответа
- Валидируют структуру JSON
- Сохраняют токен доступа автоматически
- Проверяют время ответа

### Переменные окружения
- `{{access_token}}` - автоматически сохраняется после входа
- `{{worker_id}}` - автоматически сохраняется
- `{{base_url}}` - настройте под ваш сервер
- `{{test_phone}}` - ваш тестовый номер

### Отладка
- Смотрите вкладку **Console** в Postman для логов
- Проверяйте **Tests Results** для результатов автотестов
- В **Headers** каждого запроса проверяйте заголовки авторизации

## Часто встречающиеся проблемы

### 401 Unauthorized
- Убедитесь, что токен сохранен в переменных окружения
- Проверьте, что выбрано правильное окружение
- Выполните заново аутентификацию

### 403 Profile completion required
- Сначала обновите профиль через `PUT /worker/profile/update`
- Убедитесь, что заполнены обязательные поля: name, age, about, profession_ids

### 429 Too Many Requests
- Подождите 2 минуты между отправками SMS кодов
- Это нормальное поведение rate limiting

### 422 Validation Error
- Проверьте обязательные поля в запросе
- Убедитесь в правильном формате данных

## Структура ответов

Все ответы API имеют единую структуру:
```json
{
  "success": true|false,
  "data": { /* данные */ },
  "message": "Описание результата"
}
```

## Тестирование в разных средах

### Локальная разработка
```
base_url = http://localhost/ish_top
```

### Тестовый сервер
```
base_url = https://test.yourdomain.com
```

### Продакшн
```
base_url = https://api.yourdomain.com
```

---

**Готово!** Теперь вы можете полноценно тестировать Worker API через Postman.
