<?php

namespace app\common\models;

use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;

/**
 * Модель логов действий работодателей
 * 
 * @property int $id
 * @property int $employer_id
 * @property string $action
 * @property string $context
 * @property string $device_info
 * @property string $created_at
 */
class EmployerLog extends ActiveRecord
{
    // Типы действий работодателей
    const ACTION_REGISTRATION_START = 'employer_registration_start';
    const ACTION_PROFILE_CREATED = 'employer_profile_created';
    const ACTION_VACANCY_CREATED = 'vacancy_created';
    const ACTION_VACANCY_UPDATED = 'vacancy_updated';
    const ACTION_VACANCY_DELETED = 'vacancy_deleted';
    const ACTION_VACANCY_PUBLISHED = 'vacancy_published';
    const ACTION_VACANCY_UNPUBLISHED = 'vacancy_unpublished';
    const ACTION_WORKER_SEARCHED = 'worker_searched';
    const ACTION_WORKER_VIEWED = 'worker_viewed';
    const ACTION_WORKER_FAVORITED = 'worker_favorited';
    const ACTION_WORKER_CONTACTED = 'worker_contacted';
    const ACTION_PROFILE_VIEWED = 'employer_profile_viewed';
    const ACTION_PROFILE_UPDATED = 'employer_profile_updated';
    const ACTION_ERROR_OCCURRED = 'error_occurred';
    const ACTION_MESSAGE_RECEIVED = 'message_received';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%employer_logs}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => false,
                'value' => function() {
                    return date('Y-m-d H:i:s');
                }
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['action'], 'required'],
            [['employer_id'], 'integer'],
            [['context'], 'string'],
            [['created_at'], 'safe'],
            [['action'], 'string', 'max' => 100],
            [['device_info'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'employer_id' => 'ID Работодателя',
            'action' => 'Действие',
            'context' => 'Контекст',
            'device_info' => 'Информация об устройстве',
            'created_at' => 'Дата создания',
        ];
    }

    /**
     * Логирование действия работодателя
     * 
     * @param string $action
     * @param array $context
     * @param int|null $employerId
     * @param string|null $deviceInfo
     * @return EmployerLog|false
     */
    public static function logAction($action, $context = [], $employerId = null, $deviceInfo = null)
    {
        $log = new static();
        $log->action = $action;
        $log->context = json_encode($context, JSON_UNESCAPED_UNICODE);
        $log->employer_id = $employerId;
        $log->device_info = $deviceInfo;

        if ($log->save()) {
            \Yii::info("Employer action logged: {$action} for employer_id: {$employerId}", 'employer');
            return $log;
        }

        \Yii::error("Failed to log employer action: {$action} for employer_id: {$employerId}", 'employer');
        return false;
    }

    /**
     * Логирование ошибки работодателя
     */
    public static function logError($error, $additionalData = [], $employerId = null)
    {
        $context = [
            'error' => $error,
            'additional_data' => $additionalData,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        return self::logAction(self::ACTION_ERROR_OCCURRED, $context, $employerId);
    }

    /**
     * Получение работодателя
     */
    public function getEmployer()
    {
        return $this->hasOne(\app\modules\employer\models\Employer::class, ['id' => 'employer_id']);
    }

    /**
     * Получение статистики действий работодателя
     */
    public static function getActionStats($employerId, $period = '24 HOUR')
    {
        return static::find()
            ->where(['employer_id' => $employerId])
            ->andWhere(['>=', 'created_at', new \yii\db\Expression("NOW() - INTERVAL '{$period}'")])
            ->groupBy('action')
            ->select(['action', 'COUNT(*) as count'])
            ->asArray()
            ->all();
    }

    /**
     * Получение последних логов работодателя
     */
    public static function getRecentLogs($employerId, $limit = 10)
    {
        return static::find()
            ->where(['employer_id' => $employerId])
            ->orderBy(['created_at' => SORT_DESC])
            ->limit($limit)
            ->all();
    }
} 