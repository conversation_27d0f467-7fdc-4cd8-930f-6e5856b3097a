<?php

use yii\db\Migration;

class m250626_065043_create_structura_db extends Migration
{
    public function safeUp()
    {
        // Создание таблицы industry_categories
        $this->createTable('{{%industry_categories}}', [
            'id' => $this->primaryKey(),
            'name' => $this->string(100)->notNull()->unique(), // Название категории
            'created_at' => $this->timestamp()->defaultExpression('NOW()'), // Дата создания
        ]);
        
        // Добавление комментария к таблице
        $this->addCommentOnTable('{{%industry_categories}}', 'Таблица категорий индустрий');

        // Создание индекса для поля name
        $this->createIndex('idx-industry_categories-name', '{{%industry_categories}}', 'name');

        // Создание таблицы professions
        $this->createTable('{{%professions}}', [
            'id' => $this->primaryKey(),
            'name_uz' => $this->string(100)->null(), // Kasb nomi (uz)
            'name_ru' => $this->string(100)->null(), // Название профессии (ru)
            'name_en' => $this->string(100)->null(), // Profession name (en)
            'created_at' => $this->timestamp()->defaultExpression('NOW()'), // Дата создания
            'deleted_at' => $this->timestamp(), // Дата удаления
        ]);
        
        
        // Добавление комментария к таблице
        $this->addCommentOnTable('{{%professions}}', 'Таблица профессий');

        // Создание индекса для поля name
        $this->createIndex('idx-professions-name', '{{%professions}}', 'name_uz');
        $this->createIndex('idx-professions-name_ru', '{{%professions}}', 'name_ru');
        $this->createIndex('idx-professions-name_en', '{{%professions}}', 'name_en');

        // Создание таблицы employers
        $this->createTable('{{%employers}}', [
            'id' => $this->primaryKey(),
            'name' => $this->string(100)->notNull(), // Имя работодателя
            'phone' => $this->string(20)->notNull()->unique(), // Телефон
            'business_name' => $this->string(255), // Название компании
            'business_inn' => $this->string(20)->unique(), // ИНН компании
            'business_address' => $this->string(255), // Адрес компании
            'auth_token' => $this->string(255)->unique(), // Токен авторизации
            'token_expires_at' => $this->timestamp(), // Время истечения токена
            'language' => $this->string(10), // Язык
            'status' => $this->integer(), // Статус работодателя
            'created_at' => $this->timestamp()->defaultExpression('NOW()'), // Дата создания
            'deleted_at' => $this->timestamp(), // Дата удаления
        ]);
        
        // Добавление комментария к таблице
        $this->addCommentOnTable('{{%employers}}', 'Таблица работодателей');

        // Создание индексов для полей phone и business_inn
        $this->createIndex('idx-employers-phone', '{{%employers}}', 'phone');
        $this->createIndex('idx-employers-business_inn', '{{%employers}}', 'business_inn');

        // Создание таблицы workers
        $this->createTable('{{%workers}}', [
            'id' => $this->primaryKey(),
            'chat_id' => $this->integer()->unique()->null(), // Telegram chat_id (может быть null)
            'name' => $this->string(100)->null(), // Имя работника (может быть null)
            'phone' => $this->string(20)->unique()->null(), // Телефон (может быть null)
            'age' => $this->integer()->null(), // Возраст (может быть null)
            'lat' => $this->float()->null(), // Широта (может быть null)
            'long' => $this->float()->null(), // Долгота (может быть null)
            'experience_years' => $this->integer()->null(), // Опыт работы (может быть null)
            'audio_file_url' => $this->string(255)->null(), // Ссылка на аудиофайл (может быть null)
            'about' => $this->text()->null(), // Описание (может быть null)
            'language' => $this->string(10)->null(), // Язык (может быть null)
            'status' => $this->integer(), // Статус регистрации
            'profile_status' => $this->integer()->null(), // Статус профиля (0 - не завершен, 1 - завершен)
            'auth_token' => $this->string(255)->null(), // Токен авторизации (может быть null)
            'token_expires_at' => $this->timestamp()->null(), // Время истечения токена (может быть null)
            'created_at' => $this->timestamp()->defaultExpression('NOW()'), // Дата создания
            'deleted_at' => $this->timestamp()->null(), // Дата удаления (может быть null)
        ]);
            
        // Добавление комментария к таблице
        $this->addCommentOnTable('{{%workers}}', 'Таблица работников');
        $this->addCommentOnColumn('{{%workers}}', 'auth_token', 'Токен для авторизации API');
        $this->addCommentOnColumn('{{%workers}}', 'token_expires_at', 'Время истечения токена');

         // Создаем индекс для быстрого поиска по токену
        $this->createIndex('idx-workers-auth_token', '{{%workers}}', 'auth_token');
        
        // Создание индексов для полей chat_id, phone и координат
        $this->createIndex('idx-workers-chat_id', '{{%workers}}', 'chat_id');
        $this->createIndex('idx-workers-phone', '{{%workers}}', 'phone');
        $this->createIndex('idx-workers-lat_long', '{{%workers}}', ['lat', 'long']);



        // Создание таблицы worker_professions
        $this->createTable('{{%worker_professions}}', [
            'id' => $this->primaryKey(),
            'worker_id' => $this->integer()->notNull(), // Ссылка на работника
            'profession_id' => $this->integer()->notNull(), // Ссылка на профессию
        ]);
        
        // Добавление комментария к таблице
        $this->addCommentOnTable('{{%worker_professions}}', 'Таблица связи работников и профессий');

        // Создание внешних ключей и индексов для worker_professions
        $this->addForeignKey(
            'fk-worker_professions-worker_id',
            '{{%worker_professions}}',
            'worker_id',
            '{{%workers}}',
            'id',
            'CASCADE',
            'CASCADE'
        );
        $this->addForeignKey(
            'fk-worker_professions-profession_id',
            '{{%worker_professions}}',
            'profession_id',
            '{{%professions}}',
            'id',
            'CASCADE',
            'CASCADE'
        );
        $this->createIndex('idx-worker_professions-worker_id', '{{%worker_professions}}', 'worker_id');
        $this->createIndex('idx-worker_professions-profession_id', '{{%worker_professions}}', 'profession_id');

        // Создание таблицы vacancies
        $this->createTable('{{%vacancies}}', [
            'id' => $this->primaryKey(),
            'employer_id' => $this->integer()->notNull(), // Ссылка на работодателя
            'title' => $this->string(100)->notNull(), // Название вакансии
            'lat' => $this->float(), // Широта
            'long' => $this->float(), // Долгота
            'description' => $this->text()->notNull(), // Описание
            'salary' => $this->string(50), // Зарплата
            'profession_id' => $this->integer()->notNull(), // Ссылка на профессию
            'status' => $this->integer(), // Статус вакансии
            'created_at' => $this->timestamp()->defaultExpression('NOW()'), // Дата создания
            'deleted_at' => $this->timestamp(), // Дата удаления
        ]);
        
        // Добавление комментария к таблице
        $this->addCommentOnTable('{{%vacancies}}', 'Таблица вакансий');

        // Создание внешних ключей и индексов для vacancies
        $this->addForeignKey(
            'fk-vacancies-employer_id',
            '{{%vacancies}}',
            'employer_id',
            '{{%employers}}',
            'id',
            'CASCADE',
            'CASCADE'
        );
        $this->addForeignKey(
            'fk-vacancies-profession_id',
            '{{%vacancies}}',
            'profession_id',
            '{{%professions}}',
            'id',
            'CASCADE',
            'CASCADE'
        );
        $this->createIndex('idx-vacancies-employer_id', '{{%vacancies}}', 'employer_id');
        $this->createIndex('idx-vacancies-profession_id', '{{%vacancies}}', 'profession_id');
        $this->createIndex('idx-vacancies-lat_long', '{{%vacancies}}', ['lat', 'long']);

        // Создание таблицы employer_logs
        $this->createTable('{{%employer_logs}}', [
            'id' => $this->primaryKey(),
            'employer_id' => $this->integer()->notNull(), // Ссылка на работодателя
            'action' => $this->string(100)->notNull(), // Действие
            'context' => $this->text(), // Контекст
            'device_info' => $this->string(255), // Информация об устройстве
            'created_at' => $this->timestamp()->defaultExpression('NOW()'), // Дата создания
            'deleted_at' => $this->timestamp()->null(), // Дата удаления
        ]);
        
        // Добавление комментария к таблице
        $this->addCommentOnTable('{{%employer_logs}}', 'Таблица логов действий работодателей');

        // Создание внешнего ключа и индекса для employer_logs
        $this->addForeignKey(
            'fk-employer_logs-employer_id',
            '{{%employer_logs}}',
            'employer_id',
            '{{%employers}}',
            'id',
            'CASCADE',
            'CASCADE'
        );
        $this->createIndex('idx-employer_logs-employer_id', '{{%employer_logs}}', 'employer_id');

        // Создание таблицы worker_logs
        $this->createTable('{{%worker_logs}}', [
            'id' => $this->primaryKey(),
            'worker_id' => $this->integer(), // Ссылка на работника
            'chat_id' => $this->integer()->null(), // Telegram chat_id
            'action' => $this->string(100)->notNull(), // Действие
            'context' => $this->text(), // Контекст
            'device_info' => $this->string(255), // Информация об устройстве
            'created_at' => $this->timestamp()->defaultExpression('NOW()'), // Дата создания
            'deleted_at' => $this->timestamp()->null(), // Дата удаления
        ]);
        
        // Добавление комментария к таблице
        $this->addCommentOnTable('{{%worker_logs}}', 'Таблица логов действий работников');

        // Создание внешнего ключа и индекса для worker_logs
        $this->addForeignKey(
            'fk-worker_logs-worker_id',
            '{{%worker_logs}}',
            'worker_id',
            '{{%workers}}',
            'id',
            'SET NULL',
            'CASCADE'
        );
        $this->createIndex('idx-worker_logs-worker_id', '{{%worker_logs}}', 'worker_id');
        $this->createIndex('idx-worker_logs-chat_id', '{{%worker_logs}}', 'chat_id');

        // Создание таблицы temporary_registrations
        $this->createTable('{{%temporary_registrations}}', [
            'id' => $this->primaryKey(),
            'chat_id' => $this->integer()->null()->unique(), // Telegram chat_id
            'name' => $this->string(100), // Имя
            'phone' => $this->string(20), // Телефон
            'age' => $this->integer(), // Возраст
            'lat' => $this->float(), // Широта
            'long' => $this->float(), // Долгота
            'experience_years' => $this->integer(), // Опыт работы
            'audio_file_url' => $this->string(255), // Ссылка на аудиофайл
            'about' => $this->text(), // Описание
            'language' => $this->string(10), // Язык
            'status' => $this->integer(), // Этапы регистрации
            'created_at' => $this->timestamp()->defaultExpression('NOW()'), // Дата создания
            'updated_at' => $this->timestamp()->defaultExpression('NOW()'), // Дата обновления
        ]);
        
        // Добавление комментария к таблице
        $this->addCommentOnTable('{{%temporary_registrations}}', 'Таблица временных регистраций');

        // Создание индекса для chat_id
        $this->createIndex('idx-temporary_registrations-chat_id', '{{%temporary_registrations}}', 'chat_id');

        // Создание таблицы worker_favorites
        $this->createTable('{{%worker_favorites}}', [
            'id' => $this->primaryKey(),
            'worker_id' => $this->integer()->notNull(), // Ссылка на работника
            'vacancy_id' => $this->integer()->notNull(), // Ссылка на вакансию
            'created_at' => $this->timestamp()->defaultExpression('NOW()'), // Дата добавления
            'deleted_at' => $this->timestamp(), // Дата удаления
        ]);
        
        // Добавление комментария к таблице
        $this->addCommentOnTable('{{%worker_favorites}}', 'Таблица избранных вакансий работников');

        // Создание внешних ключей и индексов для worker_favorites
        $this->addForeignKey(
            'fk-worker_favorites-worker_id',
            '{{%worker_favorites}}',
            'worker_id',
            '{{%workers}}',
            'id',
            'CASCADE',
            'CASCADE'
        );
        $this->addForeignKey(
            'fk-worker_favorites-vacancy_id',
            '{{%worker_favorites}}',
            'vacancy_id',
            '{{%vacancies}}',
            'id',
            'CASCADE',
            'CASCADE'
        );
        $this->createIndex('idx-worker_favorites-worker_id', '{{%worker_favorites}}', 'worker_id');
        $this->createIndex('idx-worker_favorites-vacancy_id', '{{%worker_favorites}}', 'vacancy_id');

        // Создание таблицы employer_favorites
        $this->createTable('{{%employer_favorites}}', [
            'id' => $this->primaryKey(),
            'employer_id' => $this->integer()->notNull(), // Ссылка на работодателя
            'worker_id' => $this->integer()->notNull(), // Ссылка на работника
            'created_at' => $this->timestamp()->defaultExpression('NOW()'), // Дата добавления
            'deleted_at' => $this->timestamp(), // Дата удаления
        ]);
        
        // Добавление комментария к таблице
        $this->addCommentOnTable('{{%employer_favorites}}', 'Таблица избранных работников работодателей');

        // Создание внешних ключей и индексов для employer_favorites
        $this->addForeignKey(
            'fk-employer_favorites-employer_id',
            '{{%employer_favorites}}',
            'employer_id',
            '{{%employers}}',
            'id',
            'CASCADE',
            'CASCADE'
        );
        $this->addForeignKey(
            'fk-employer_favorites-worker_id',
            '{{%employer_favorites}}',
            'worker_id',
            '{{%workers}}',
            'id',
            'CASCADE',
            'CASCADE'
        );
        $this->createIndex('idx-employer_favorites-employer_id', '{{%employer_favorites}}', 'employer_id');
        $this->createIndex('idx-employer_favorites-worker_id', '{{%employer_favorites}}', 'worker_id');

        // Создание таблицы worker_ratings
        $this->createTable('{{%worker_ratings}}', [
            'id' => $this->primaryKey(),
            'worker_id' => $this->integer()->notNull(), // Ссылка на работника
            'profile_completion' => $this->boolean()->defaultValue(false), // Завершенность профиля
            'rating_score' => $this->float()->defaultValue(0.0), // Оценка
            'created_at' => $this->timestamp()->defaultExpression('NOW()'), // Дата создания
            'deleted_at' => $this->timestamp(), // Дата удаления
        ]);
        
        // Добавление комментария к таблице
        $this->addCommentOnTable('{{%worker_ratings}}', 'Таблица рейтингов работников');

        // Создание внешнего ключа и индекса для worker_ratings
        $this->addForeignKey(
            'fk-worker_ratings-worker_id',
            '{{%worker_ratings}}',
            'worker_id',
            '{{%workers}}',
            'id',
            'CASCADE',
            'CASCADE'
        );
        $this->createIndex('idx-worker_ratings-worker_id', '{{%worker_ratings}}', 'worker_id');

        // Создание таблицы search_queries
        $this->createTable('{{%search_queries}}', [
            'id' => $this->primaryKey(),
            'user_type' => $this->integer(), // Тип пользователя
            'user_id' => $this->integer(), // Ссылка на пользователя
            'chat_id' => $this->string(50), // Telegram chat_id
            'query_text' => $this->string(255)->notNull(), // Текст запроса
            'created_at' => $this->timestamp()->defaultExpression('NOW()'), // Дата создания
        ]);
        
        // Добавление комментария к таблице
        $this->addCommentOnTable('{{%search_queries}}', 'Таблица поисковых запросов');

        // Создание индексов для user_id и chat_id
        $this->createIndex('idx-search_queries-user_id', '{{%search_queries}}', 'user_id');
        $this->createIndex('idx-search_queries-chat_id', '{{%search_queries}}', 'chat_id');
    }

    /**
     * Удаление таблиц в обратном порядке
     */
    public function safeDown()
    {
        // Удаление таблиц в обратном порядке для корректной обработки внешних ключей
        $this->dropTable('{{%search_queries}}');
        $this->dropTable('{{%worker_ratings}}');
        $this->dropTable('{{%employer_favorites}}');
        $this->dropTable('{{%worker_favorites}}');
        $this->dropTable('{{%temporary_registrations}}');
        $this->dropTable('{{%worker_logs}}');
        $this->dropTable('{{%employer_logs}}');
        $this->dropTable('{{%vacancies}}');
        $this->dropTable('{{%worker_professions}}');
        $this->dropTable('{{%workers}}');
        $this->dropTable('{{%employers}}');
        $this->dropTable('{{%professions}}');
        $this->dropTable('{{%industry_categories}}');
    }
}
