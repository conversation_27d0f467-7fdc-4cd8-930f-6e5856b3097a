<?php
/**
 * Конфигурация маршрутов приложения
 */

$routes = [];

// ========================================
// Telegram маршруты (не трогаем)
// ========================================
$routes['telegram/webhook'] = 'telegram/webhook';
$routes['telegram/registration/<action:\w+>'] = 'telegram/registration/<action>';
$routes['telegram/profession/<action:\w+>'] = 'telegram/profession/<action>';


// ========================================
// Worker API маршруты (вручную)
// ========================================
// Auth
$routes['POST worker/auth/send-code'] = 'worker/auth/send-code';
$routes['POST worker/auth/login'] = 'worker/auth/login';
$routes['POST worker/auth/logout'] = 'worker/auth/logout';
$routes['GET worker/auth/verify'] = 'worker/auth/verify';
$routes['POST worker/auth/refresh'] = 'worker/auth/refresh';

// Vacancy
$routes['GET worker/vacancy/list'] = 'worker/vacancy/list';
$routes['GET worker/vacancy/search'] = 'worker/vacancy/search';
$routes['GET worker/vacancy/detail/<id:\d+>'] = 'worker/vacancy/detail';

// Profile
$routes['GET worker/profile/index'] = 'worker/profile/index';
$routes['PUT worker/profile/update'] = 'worker/profile/update';
$routes['POST worker/profile/upload-audio'] = 'worker/profile/upload-audio';

// ========================================
// Employer API маршруты
// ========================================
// Auth
$routes['POST employer/auth/send-code'] = 'employer/auth/send-code';
$routes['POST employer/auth/verify-code'] = 'employer/auth/verify-code';
$routes['POST employer/auth/complete-registration'] = 'employer/auth/complete-registration';
$routes['GET employer/auth/status'] = 'employer/auth/status';
$routes['POST employer/auth/logout'] = 'employer/auth/logout';

// Workers (просмотр работников)
$routes['GET employer/worker/list'] = 'employer/worker/index';
$routes['GET employer/worker/search'] = 'employer/worker/index';
$routes['GET employer/worker/detail'] = 'employer/worker/view';
$routes['GET employer/worker/professions'] = 'employer/worker/professions';
$routes['GET employer/worker/by-profession'] = 'employer/worker/by-profession';
$routes['GET employer/worker/statistics'] = 'employer/worker/statistics';
$routes['GET employer/worker/check-access'] = 'employer/worker/check-access';

// Favorites (избранные работники)
$routes['POST employer/favorite/add'] = 'employer/favorite/add';
$routes['POST employer/favorite/remove'] = 'employer/favorite/remove';
$routes['GET employer/favorite/list'] = 'employer/favorite/index';
$routes['POST employer/favorite/toggle'] = 'employer/favorite/toggle';
$routes['POST employer/favorite/bulk-add'] = 'employer/favorite/bulk-add';
$routes['POST employer/favorite/bulk-remove'] = 'employer/favorite/bulk-remove';
$routes['GET employer/favorite/statistics'] = 'employer/favorite/statistics';
$routes['GET employer/favorite/by-profession'] = 'employer/favorite/by-profession';

// Profile (профиль работодателя)
$routes['GET employer/profile/view'] = 'employer/profile/view';
$routes['PUT employer/profile/update'] = 'employer/profile/update';
$routes['POST employer/profile/update'] = 'employer/profile/update';
$routes['POST employer/profile/change-language'] = 'employer/profile/change-language';
$routes['GET employer/profile/languages'] = 'employer/profile/languages';
$routes['POST employer/profile/delete'] = 'employer/profile/delete';

// Vacancy (управление вакансиями)
$routes['GET employer/vacancy/list'] = 'employer/vacancy/list';
$routes['GET employer/vacancy/view'] = 'employer/vacancy/view';
$routes['POST employer/vacancy/create'] = 'employer/vacancy/create';
$routes['PUT employer/vacancy/update'] = 'employer/vacancy/update';
$routes['POST employer/vacancy/update'] = 'employer/vacancy/update';
$routes['DELETE employer/vacancy/delete'] = 'employer/vacancy/delete';
$routes['POST employer/vacancy/delete'] = 'employer/vacancy/delete';
$routes['GET employer/vacancy/statuses'] = 'employer/vacancy/statuses';

// Старые маршруты для совместимости
$routes['employer/<action:\w+>'] = 'employer/default/<action>';
$routes['employer/vacancy/<action:\w+>'] = 'employer/vacancy/<action>';

// ========================================
// Основные маршруты приложения
// ========================================
$routes[''] = 'site/index';
$routes['<action:\w+>'] = 'site/<action>';

return $routes;
