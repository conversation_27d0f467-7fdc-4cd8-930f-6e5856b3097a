<?php

use yii\db\Migration;

class m250626_075421_add_data_for_profession_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {

        $this->batchInsert('{{%professions}}', ['name_uz', 'name_ru', 'name_en'], [
            ['<PERSON><PERSON><PERSON><PERSON>', 'Водитель', 'Driver'],
            ['<PERSON><PERSON><PERSON>', '<PERSON>у<PERSON>ье<PERSON>', 'Courier'],
            ['<PERSON><PERSON><PERSON><PERSON>', 'Продавец', 'Salesperson'],
            ['Kassir', 'Кассир', 'Cashier'],
            ['Qo‘riqchi', 'Охранник', 'Security guard'],
            ['<PERSON><PERSON><PERSON>chi', 'Уборщик', 'Cleaner'],
            ['<PERSON><PERSON><PERSON>z', 'Повар', 'Cook'],
            ['Ofitsiant', 'Официант', 'Waiter'],
            ['<PERSON>k tashuvchi', 'Грузчик', 'Loader'],
            ['Quruvchi', 'Строитель', 'Builder'],
            ['<PERSON><PERSON><PERSON><PERSON>', 'Электрик', '<PERSON>ian'],
            ['<PERSON><PERSON><PERSON>', 'Сантехник', 'Plumber'],
            ['<PERSON>‘yoqchi', 'Маляр', 'Painter'],
            ['Mexanik', 'Слесарь', 'Fitter'],
            ['Payvandchi', 'Сварщик', 'Welder'],
            ['Dasturchi', 'Программист', 'Programmer'],
            ['Menejer', 'Менеджер', 'Manager'],
            ['Administrator', 'Администратор', 'Administrator'],
            ['Buxgalter', 'Бухгалтер', 'Accountant'],
            ['Dizayner', 'Дизайнер', 'Designer'],
        ]);

        // Мок-работодатель
            $this->insert('{{%employers}}', [
                'name' => 'Ali Valiyev',
                'phone' => '+************',
                'business_name' => 'IT Kompaniya',
                'business_inn' => '*********',
                'business_address' => 'Toshkent, Chilonzor',
                'status' => 1,
            ]);



            $this->batchInsert('{{%vacancies}}', [
                'employer_id', 'title', 'lat', 'long', 'description', 'salary', 'profession_id', 'status'
            ], [
                [1, 'Haydovchi kerak', 41.3111, 69.2797, 'Yaxshi haydovchi kerak Toshkent shahrida.', '5 000 000 UZS', 1, 1],
                [1, 'Kuryer ishga taklif qilinadi', 41.3200, 69.2500, 'Mototsikl bilan yetkazib berish xizmati.', '3 500 000 UZS', 2, 1],
                [1, 'Sotuvchi kerak do‘konga', 41.3050, 69.2400, 'Chilonzor tumani, oziq-ovqat do‘koniga.', '2 800 000 UZS', 3, 1],
                [1, 'Kassir kerak supermarketga', 41.3300, 69.3000, 'Sergeli tumanidagi supermarketga kassir.', '3 000 000 UZS', 4, 1],
                [1, 'Ofitsiant ishga qabul qilinadi', 41.3123, 69.2701, 'Restoranga xizmat ko‘rsatuvchi kerak.', '4 200 000 UZS', 8, 1],
                [1, 'Tozalovchi ayol kerak', 41.3145, 69.2803, 'Ofis uchun tozalovchi ishga olinadi.', '2 500 000 UZS', 6, 1],
                [1, 'Oshpaz kerak', 41.3167, 69.2555, 'Milliy taom tayyorlaydigan oshpaz kerak.', '6 000 000 UZS', 7, 1],
                [1, 'Yuk tashuvchi erkak kerak', 41.3088, 69.2480, 'Omborga yuk tashuvchi kerak.', '3 000 000 UZS', 9, 1],
                [1, 'Santexnik xizmatlari kerak', 41.3266, 69.2900, 'Ofisdagi muammolarni hal qilish.', '4 500 000 UZS', 12, 1],
                [1, 'Dizayner ishga taklif qilinadi', 41.3211, 69.2600, 'Grafik dizayn ishlari uchun mutaxassis.', '7 000 000 UZS', 20, 1],
            ]);


        
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        
    }

    
}
