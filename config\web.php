<?php

$params = require __DIR__ . '/params.php';
$db = require __DIR__ . '/db.php';

$config = [
    'id' => 'basic',
    'basePath' => dirname(__DIR__),
    'bootstrap' => ['log'],
    'aliases' => [
        '@bower' => '@vendor/bower-asset',
        '@npm'   => '@vendor/npm-asset',
    ],
    'components' => [
        'request' => [
            // !!! insert a secret key in the following (if it is empty) - this is required by cookie validation
            'cookieValidationKey' => $params['cookieValidationKey'],
            'enableCsrfValidation' => false, // Отключаем CSRF защиту для API
        ],
        'cache' => [
            'class' => 'yii\caching\FileCache',
        ],
        'user' => [
            'identityClass' => 'app\models\User',
            'enableAutoLogin' => true,
        ],
        'errorHandler' => [
            'errorAction' => 'site/error',
        ],
        'mailer' => [
            'class' => \yii\symfonymailer\Mailer::class,
            'viewPath' => '@app/mail',
            // send all mails to a file by default.
            'useFileTransport' => true,
        ],
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning'],
                    'logFile' => '@runtime/logs/app.log',
                ],
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['info', 'error', 'warning'],
                    'categories' => ['telegram'],
                    'logFile' => '@runtime/logs/telegram.log',
                    'maxFileSize' => 1024 * 10,
                    'maxLogFiles' => 10,
                    'exportInterval' => 1, // Экспорт каждого сообщения
                    'logVars' => [], // Убираем $_SERVER, $_POST и т.д. для чистоты
                    'enabled' => true,
                    'microtime' => true, // Точное время с микросекундами
                ],
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning'],
                    'categories' => ['employer'],
                    'logFile' => '@runtime/logs/employer.log',
                    'maxFileSize' => 1024 * 10,
                    'maxLogFiles' => 10,
                    'exportInterval' => 1,
                    'logVars' => [],
                    'enabled' => true,
                    'microtime' => true,
                ],
            ],
        ],
        'db' => $db,
        'telegramService' => [
            'class' => 'app\modules\telegram\models\TelegramService',
            'botToken' => $params['telegramBotToken'],
        ],
        
        'urlManager' => [
            'enablePrettyUrl' => true,
            'showScriptName' => false,
            'rules' => require __DIR__ . '/routes.php',
        ],
        
    ],
    'modules' => [
        'telegram' => [
            'class' => 'app\modules\telegram\Module',
        ],
        'worker' => [
            'class' => 'app\modules\worker\Module',
        ],
        'employer' => [
            'class' => 'app\modules\employer\Module',
        ],
    ],
    'params' => $params,
];

if (YII_ENV_DEV) {
    // configuration adjustments for 'dev' environment
    $config['bootstrap'][] = 'debug';
    $config['modules']['debug'] = [
        'class' => 'yii\debug\Module',
        // uncomment the following to add your IP if you are not connecting from localhost.
        //'allowedIPs' => ['127.0.0.1', '::1'],
    ];

    $config['bootstrap'][] = 'gii';
    $config['modules']['gii'] = [
        'class' => 'yii\gii\Module',
        // uncomment the following to add your IP if you are not connecting from localhost.
        //'allowedIPs' => ['127.0.0.1', '::1'],
    ];
}

return $config;
